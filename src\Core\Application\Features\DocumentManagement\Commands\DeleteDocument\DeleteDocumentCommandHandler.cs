using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.DocumentManagement.Commands.DeleteDocument
{
    /// <summary>
    /// Handler for deleting documents
    /// </summary>
    public class DeleteDocumentCommandHandler : BaseResponseHandler, ICommandHandler<DeleteDocumentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public DeleteDocumentCommandHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<string>> Handle(DeleteDocumentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document deletion for document ID: {request.DocumentId} by user {_currentUserService.UserId}");

                // Get document
                var document = await _repositoryManager.DocumentRepository.GetByIdAsync<Document>(request.DocumentId, false);
                if (document == null)
                {
                    return NotFound<string>(_localizer["DocumentNotFound"]);
                }

                // Check permissions - user can delete their own documents or admin can delete any
                var currentUser = await _repositoryManager.UserRepository.GetByIdAsync(_currentUserService.UserId ?? 1);
                var userRoles = await _repositoryManager.UserRepository.GetUserRolesAsync(_currentUserService.UserId ?? 1);
                
                bool canDelete = document.UploadedByUserId == (_currentUserService.UserId ?? 1) ||
                               userRoles.Any(r => r.Equals("Admin", StringComparison.OrdinalIgnoreCase) ||
                                                r.Equals("Super Admin", StringComparison.OrdinalIgnoreCase));

                if (!canDelete)
                {
                    return Unauthorized<string>(_localizer["UnauthorizedToDeleteDocument"]);
                }

                // If permanent delete, remove file from disk
                if (request.PermanentDelete)
                {
                    try
                    {
                        var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", document.FilePath);
                        if (File.Exists(fullPath))
                        {
                            File.Delete(fullPath);
                            _logger.LogInfo($"Physical file deleted: {fullPath}");
                        }
                    }
                    catch (Exception fileEx)
                    {
                        _logger.LogWarn($"Could not delete physical file: {fileEx.Message}");
                        // Continue with database deletion even if file deletion fails
                    }
                }

                // Remove from database
                await _repositoryManager.DocumentRepository.DeleteAsync(document);
                await _repositoryManager.SaveAsync();

                _logger.LogInfo($"Document deleted successfully. ID: {document.Id}, Name: {document.Name}");

                return Success(_localizer["DocumentDeletedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting document ID {request.DocumentId}: {ex.Message}");
                return ServerError<string>(_localizer["DocumentDeletionFailed"]);
            }
        }
        #endregion
    }
}

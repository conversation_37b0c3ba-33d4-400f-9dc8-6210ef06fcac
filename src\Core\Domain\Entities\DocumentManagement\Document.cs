using Domain.Entities.Base;
using Domain.Entities.Shared;
using Domain.Entities.Users;

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Represents a document in the document management system
    /// </summary>
    public class Document : FullAuditedEntity
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the attachment containing the file
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Navigation property to attachment
        /// </summary>
        public virtual Attachment Attachment { get; set; } = null!;

        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Navigation property to document category
        /// </summary>
        public virtual DocumentCategory DocumentCategory { get; set; } = null!;

        /// <summary>
        /// User who uploaded the document
        /// </summary>
        public int UploadedByUserId { get; set; }

        /// <summary>
        /// Navigation property to user who uploaded
        /// </summary>
        public virtual User UploadedByUser { get; set; } = null!;

        /// <summary>
        /// Document description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Document version number
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// Whether document is active/visible
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Document tags for search/filtering
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Access level (Public, Private, Restricted)
        /// </summary>
        public DocumentAccessLevel AccessLevel { get; set; } = DocumentAccessLevel.Private;

        /// <summary>
        /// Download count for analytics
        /// </summary>
        public int DownloadCount { get; set; } = 0;

        /// <summary>
        /// Last accessed date
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }
    }

    /// <summary>
    /// Document access levels
    /// </summary>
    public enum DocumentAccessLevel
    {
        /// <summary>
        /// Accessible to all users
        /// </summary>
        Public = 1,

        /// <summary>
        /// Accessible only to uploader and admins
        /// </summary>
        Private = 2,

        /// <summary>
        /// Accessible to specific roles/users
        /// </summary>
        Restricted = 3
    }
}

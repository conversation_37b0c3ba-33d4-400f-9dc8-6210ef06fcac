using Domain.Entities.Base;
using Domain.Entities.Shared;
using Domain.Entities.Users;

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Represents a document in the document management system
    /// </summary>
    public class Document : FullAuditedEntity
    {
        /// <summary>
        /// Reference to the attachment containing the file
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Navigation  property to attachment
        /// </summary>
        public virtual Attachment Attachment { get; set; } = null!;

        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Navigation property to document category
        /// </summary>
        public virtual DocumentCategory DocumentCategory { get; set; } = null!;
    }
}

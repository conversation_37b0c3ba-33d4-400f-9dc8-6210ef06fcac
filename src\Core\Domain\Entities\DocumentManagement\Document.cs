using Domain.Entities.Base;
using Domain.Entities.Shared;
using Domain.Entities.Users;

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Represents a document in the document management system
    /// </summary>
    public class Document : FullAuditedEntity
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the attachment containing the file
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Navigation property to attachment
        /// </summary>
        public virtual Attachment Attachment { get; set; } = null!;

        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Navigation property to document category
        /// </summary>
        public virtual DocumentCategory DocumentCategory { get; set; } = null!;

        /// <summary>
        /// Document description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Document tags (comma-separated)
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Access level for the document
        /// </summary>
        public DocumentAccessLevel AccessLevel { get; set; } = DocumentAccessLevel.Private;

        /// <summary>
        /// User who uploaded the document
        /// </summary>
        public int UploadedByUserId { get; set; }

        /// <summary>
        /// Navigation property to user who uploaded
        /// </summary>
        public virtual User UploadedByUser { get; set; } = null!;

        /// <summary>
        /// Document version number
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// Number of times document has been downloaded
        /// </summary>
        public int DownloadCount { get; set; } = 0;

        /// <summary>
        /// Last time document was accessed
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }
    }
}

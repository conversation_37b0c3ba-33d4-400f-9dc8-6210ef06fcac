using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using System.Text.RegularExpressions;
using Application.Features.Identity.Users.Commands.EditUser;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for EditUserCommand with Sprint 3 enhancements
    /// Implements mobile number validation and unique role checking with current user exclusion
    /// </summary>
    public class EditUserValidator : AbstractValidator<EditUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        public EditUserValidator(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            Include(new BaseUserValidator(_localizer));
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {
            // User ID Validation
            RuleFor(x => x.Id)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField]);
            // Role Validation
            RuleFor(x => x.Roles)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired])
                .Must(roles => roles.Count > 0)
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired]);
            //// File Validation
            //RuleFor(x => x.CVFile)
            //    .Must(BeValidCVFile)
            //    .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidCVFile])
            //    .When(x => x.CVFile != null);

            //RuleFor(x => x.PersonalPhoto)
            //    .Must(BeValidPhotoFile)
            //    .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidPhotoFile])
            //    .When(x => x.PersonalPhoto != null);
        }
        
    }
}

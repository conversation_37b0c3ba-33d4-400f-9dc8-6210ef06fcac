﻿using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Resources;
using System.Security.Claims;
namespace Application.Features.Identity.Authentications.Commands.SignOut
{
    /// <summary>
    /// Handler for user logout
    /// Enhanced for Sprint 3 with proper session termination and audit logging
    /// </summary>
    public class SignOutCommandHandler : BaseResponseHandler, ICommandHandler<SignOutCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly IStringLocalizer<SharedResources> _localizer;
        // TODO: Add IAuditLogService when available
        // TODO: Add ITokenBlacklistService when available
        #endregion

        #region Constructors
        public SignOutCommandHandler(
            IIdentityServiceManager identityServiceManager,
            ICurrentUserService currentUserService,
            IStringLocalizer<SharedResources> localizer)
        {
            _identityServiceManager = identityServiceManager;
            _currentUserService = currentUserService;
            _localizer = localizer;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(SignOutCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var userId = _currentUserService.UserId.GetValueOrDefault();
                if (userId == 0)
                {
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                var user = await _identityServiceManager.UserManagmentService.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return NotFound<string>(_localizer[SharedResourcesKey.UserNotFound]);
                }

                // Sprint 3 Enhancement: Comprehensive session termination

                // 1. Remove FCM tokens (existing functionality)
                var userClaims = await _identityServiceManager.UserManagmentService.GetClaimsAsync(user);
                var fcmClaims = userClaims.Where(c => c.Type == CustomClaimTypes.FCMWebToken).ToList();
                if (fcmClaims.Any())
                {
                    await _identityServiceManager.UserManagmentService.RemoveClaimsAsync(user, fcmClaims);
                }

                // 2. TODO: Add access token to blacklist (when token blacklist service is available)
                // if (!string.IsNullOrEmpty(request.AccessToken))
                // {
                //     await _tokenBlacklistService.BlacklistTokenAsync(request.AccessToken);
                // }

                // 3. TODO: Invalidate refresh token (when refresh token service is available)
                // if (!string.IsNullOrEmpty(request.RefreshToken))
                // {
                //     await _refreshTokenService.InvalidateTokenAsync(request.RefreshToken);
                // }

                // 4. Update user's security stamp to invalidate existing tokens
                await _identityServiceManager.UserManagmentService.UpdateSecurityStampAsync(user);

                // 5. Sprint 3 Enhancement: Audit logging
                // TODO: Add audit log entry when audit service is available
                // await _auditLogService.LogUserActionAsync(userId, "User Logout",
                //     $"User {user.UserName} logged out at {DateTime.UtcNow}", "Logout");

                return Success<string>(_localizer[SharedResourcesKey.LogoutSuccessful]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.LogoutSystemError]);
            }
        }

        #endregion

    }
}

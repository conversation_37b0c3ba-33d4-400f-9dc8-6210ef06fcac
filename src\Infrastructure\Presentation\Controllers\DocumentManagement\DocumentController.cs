using Application.Features.DocumentManagement.Commands.DeleteDocument;
using Application.Features.DocumentManagement.Commands.UploadDocument;
using Application.Features.DocumentManagement.Queries.DownloadDocument;
using Application.Features.DocumentManagement.Queries.GetDocuments;
using Application.Features.DocumentManagement.Queries.GetDocumentCategories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers.DocumentManagement
{
    /// <summary>
    /// Controller for document management operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DocumentController : AppControllerBase
    {
        /// <summary>
        /// Get documents with filtering and pagination
        /// </summary>
        /// <param name="query">Query parameters for filtering documents</param>
        /// <returns>Paginated list of documents</returns>
        [HttpGet]
        public async Task<IActionResult> GetDocuments([FromQuery] GetDocumentsQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get document categories
        /// </summary>
        /// <param name="query">Query parameters for categories</param>
        /// <returns>List of document categories</returns>
        [HttpGet("categories")]
        public async Task<IActionResult> GetDocumentCategories([FromQuery] GetDocumentCategoriesQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Upload a new document
        /// </summary>
        /// <param name="command">Upload document command with file and metadata</param>
        /// <returns>Upload result with document information</returns>
        [HttpPost("upload")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> UploadDocument([FromForm] UploadDocumentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Download a document
        /// </summary>
        /// <param name="documentId">Document ID to download</param>
        /// <returns>File content for download</returns>
        [HttpGet("{documentId}/download")]
        public async Task<IActionResult> DownloadDocument(int documentId)
        {
            var query = new DownloadDocumentQuery { DocumentId = documentId };
            var response = await Mediator.Send(query);

            if (!response.Succeeded || response.Data == null)
            {
                return NotFound(response.Message);
            }

            var result = response.Data;
            if (!result.FileExists)
            {
                return NotFound(result.ErrorMessage ?? "File not found");
            }

            return File(result.FileContent, result.ContentType, result.FileName);
        }

        /// <summary>
        /// Preview a document (doesn't increment download count)
        /// </summary>
        /// <param name="documentId">Document ID to preview</param>
        /// <returns>File content for preview</returns>
        [HttpGet("{documentId}/preview")]
        public async Task<IActionResult> PreviewDocument(int documentId)
        {
            var query = new DownloadDocumentQuery { DocumentId = documentId, IsPreview = true };
            var response = await Mediator.Send(query);

            if (!response.Succeeded || response.Data == null)
            {
                return NotFound(response.Message);
            }

            var result = response.Data;
            if (!result.FileExists)
            {
                return NotFound(result.ErrorMessage ?? "File not found");
            }

            // Set headers for inline display
            Response.Headers.Add("Content-Disposition", $"inline; filename=\"{result.FileName}\"");
            return File(result.FileContent, result.ContentType);
        }

        /// <summary>
        /// Delete a document
        /// </summary>
        /// <param name="documentId">Document ID to delete</param>
        /// <param name="permanentDelete">Whether to permanently delete the file from disk</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{documentId}")]
        public async Task<IActionResult> DeleteDocument(int documentId, [FromQuery] bool permanentDelete = false)
        {
            var command = new DeleteDocumentCommand 
            { 
                DocumentId = documentId, 
                PermanentDelete = permanentDelete 
            };
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Get documents by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Documents in the specified category</returns>
        [HttpGet("category/{categoryId}")]
        public async Task<IActionResult> GetDocumentsByCategory(
            int categoryId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 10)
        {
            var query = new GetDocumentsQuery
            {
                CategoryId = categoryId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Search documents
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="categoryId">Optional category filter</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Search results</returns>
        [HttpGet("search")]
        public async Task<IActionResult> SearchDocuments(
            [FromQuery] string searchTerm,
            [FromQuery] int? categoryId = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            var query = new GetDocumentsQuery
            {
                SearchTerm = searchTerm,
                CategoryId = categoryId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get recent documents
        /// </summary>
        /// <param name="count">Number of recent documents to retrieve</param>
        /// <returns>Recent documents</returns>
        [HttpGet("recent")]
        public async Task<IActionResult> GetRecentDocuments([FromQuery] int count = 10)
        {
            var query = new GetDocumentsQuery
            {
                PageSize = count,
                SortBy = "CreatedAt",
                SortDirection = "desc"
            };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get popular documents (by download count)
        /// </summary>
        /// <param name="count">Number of popular documents to retrieve</param>
        /// <returns>Popular documents</returns>
        [HttpGet("popular")]
        public async Task<IActionResult> GetPopularDocuments([FromQuery] int count = 10)
        {
            var query = new GetDocumentsQuery
            {
                PageSize = count,
                SortBy = "DownloadCount",
                SortDirection = "desc"
            };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get my documents (uploaded by current user)
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Documents uploaded by current user</returns>
        [HttpGet("my-documents")]
        public async Task<IActionResult> GetMyDocuments(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            // The handler will automatically filter by current user
            var query = new GetDocumentsQuery
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SortBy = "CreatedAt",
                SortDirection = "desc"
            };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }
    }
}

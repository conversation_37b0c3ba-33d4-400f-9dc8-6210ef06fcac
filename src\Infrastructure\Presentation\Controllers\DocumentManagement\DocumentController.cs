using Application.Features.DocumentManagement.Commands.DeleteDocument;
using Application.Features.DocumentManagement.Commands.UploadDocument;
using Application.Features.DocumentManagement.Queries.List;
using Application.Features.DocumentManagement.Queries.GetDocumentCategories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers.DocumentManagement
{
    /// <summary>
    /// Controller for document management operations
    /// </summary>
    [Route("api/[controller]")] 
    [ApiController]
    [AllowAnonymous]
    public class DocumentController : AppControllerBase
    {
        /// <summary>
        /// Get documents with filtering and pagination
        /// </summary>
        /// <param name="query">Query parameters for filtering documents</param>
        /// <returns>Paginated list of documents</returns>
        [HttpGet]
        public async Task<IActionResult> GetDocuments([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get document categories
        /// </summary>
        /// <param name="query">Query parameters for categories</param>
        /// <returns>List of document categories</returns>
        [HttpGet("categories")]
        public async Task<IActionResult> GetDocumentCategories([FromQuery] GetDocumentCategoriesQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Upload a new document
        /// </summary>
        /// <param name="command">Upload document command with file and metadata</param>
        /// <returns>Upload result with document information</returns>
        [HttpPost("upload")]
        public async Task<IActionResult> UploadDocument([FromForm] UploadDocumentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        ///// <summary>
        ///// Preview a document (doesn't increment download count)
        ///// </summary>
        ///// <param name="documentId">Document ID to preview</param>
        ///// <returns>File content for preview</returns>
        //[HttpGet("{documentId}/preview")]
        //public async Task<IActionResult> PreviewDocument(int documentId)
        //{
        //    var query = new DownloadDocumentQuery { DocumentId = documentId, IsPreview = true };
        //    var response = await Mediator.Send(query);

        //    return File(response.Data.FileContent, response.Data.ContentType);
        //}

        /// <summary>
        /// Delete a document
        /// </summary>
        /// <param name="documentId">Document ID to delete</param>
        /// <param name="permanentDelete">Whether to permanently delete the file from disk</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{documentId}")]
        public async Task<IActionResult> DeleteDocument(int documentId)
        {
            var command = new DeleteDocumentCommand 
            { 
                DocumentId = documentId 
            };
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
    }
}

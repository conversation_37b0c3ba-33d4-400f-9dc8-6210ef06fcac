﻿using Domain.Helpers;
using Application.Base.Abstracts;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.Extensions.Localization;
using Resources;
namespace Application.Features.Identity.Authentications.Commands.SignIn
{
    public class SignInCommandHandler : BaseResponseH<PERSON><PERSON>, ICommandHandler<SignInCommand, BaseResponse<JwtAuthResponse>>
    {
        #region Fields
        private readonly SignInManager<User> _signInManager;
        private readonly IIdentityServiceManager _service;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructors
        public SignInCommandHandler(
            SignInManager<User> signInManager,
            IIdentityServiceManager service,
            IStringLocalizer<SharedResources> localizer)
        {
            _signInManager = signInManager;
            _service = service;
            _localizer = localizer;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<JwtAuthResponse>> Handle(SignInCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Find user by username
                var user = await _service.UserManagmentService.FindByNameAsync(request.UserName);
                if (user == null)
                    return NotFound<JwtAuthResponse>(_localizer[SharedResourcesKey.LoginUserNotFound]);

                // Check if account is deactivated (locked out)
                if (!user.IsActive)
                {
                    return BadRequest<JwtAuthResponse>(_localizer[SharedResourcesKey.LoginAccountDeactivated]);
                }
                // Attempt sign in
                var signInResult = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
                if (!signInResult.Succeeded)
                {
                    return BadRequest<JwtAuthResponse>(_localizer[SharedResourcesKey.LoginIncorrectPassword]);
                }
                // Generate JWT token
                var accessToken = await _service.AuthenticationService.GetJwtToken(user);
                accessToken.IsFirstLogin = false;
                accessToken.UserId = user.Id;
                // Check if user needs to complete registration (first-time login)
                if (!user.RegistrationIsCompleted)
                {
                    
                    accessToken.IsFirstLogin = true; // Frontend should handle this
                }

                return Success(accessToken);
            }
            catch (Exception ex)
            {
                return ServerError<JwtAuthResponse>(ex.Message);
            }
        }

        #endregion

    }
}

using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Common.Helpers;
using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using System.Globalization;

namespace Application.Features.DocumentManagement.Queries.List
{
    /// <summary>
    /// Handler for ListQuery
    /// Implements business logic for retrieving documents with filtering
    /// Follows Clean Architecture and CQRS patterns
    /// Based on BoardMembers ListQueryHandler pattern
    /// </summary>
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, PaginatedResult<DocumentDto>>
    {
        #region Fields

        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ILoggerManager _logger;

        #endregion

        #region Constructor

        public ListQueryHandler(
            IRepositoryManager repository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ILoggerManager logger)
        {
            _repository = repository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _localizer = localizer;
            _logger = logger;
        }

        #endregion

        #region Handler Implementation

        public async Task<PaginatedResult<DocumentDto>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting GetDocuments operation for user: {_currentUserService.UserId}");
                
                if (request == null)
                    return PaginatedResult<DocumentDto>.ServerError(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);

                // 1. Get user roles for permission checking
                var userRoles = await _repository.UserRepository.GetUserRolesAsync(_currentUserService.UserId ?? 1);
                bool isAdmin = userRoles.Any(r => r.Equals("Admin", StringComparison.OrdinalIgnoreCase) || 
                                                r.Equals("Super Admin", StringComparison.OrdinalIgnoreCase));

                // 2. Get documents based on filters
                var result = await _repository.DocumentRepository.GetDocumentsWithFiltersAsync(
                    categoryId: request.CategoryId,
                    searchTerm: request.Search, // Use Search from BaseListDto
                    fileExtension: request.FileExtension,
                    accessLevel: (int?)request.AccessLevel,
                    uploadedByUserId: request.UploadedByUserId,
                    isActive: request.IsActive,
                    fromDate: request.FromDate,
                    toDate: request.ToDate,
                    tags: request.Tags,
                    currentUserId: _currentUserService.UserId ?? 1,
                    isAdmin: isAdmin);

                if (!result.Any())
                {
                    return PaginatedResult<DocumentDto>.EmptyCollection(_localizer[SharedResourcesKey.NoRecords]);
                }

                // 3. Apply sorting
                var sortedResult = ApplySorting(result, request.SortBy, request.SortDirection);

                // 4. Project to DTOs and apply pagination
                var documentList = await _mapper.ProjectTo<DocumentDto>(sortedResult)
                    .ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);

                // 5. Set additional properties for each document
                foreach (var dto in documentList.Data)
                {
                    var document = sortedResult.FirstOrDefault(d => d.Id == dto.Id);
                    if (document != null)
                    {
                        dto.FormattedFileSize = FormatFileSize(document.Attachment.FileSize);
                        dto.CategoryName = GetLocalizedCategoryName(document.DocumentCategory);
                        dto.UploadedByUserName = document.UploadedByUser?.FullName ?? "Unknown";
                        dto.AccessLevel = GetAccessLevelName(document.AccessLevel);
                        dto.DownloadUrl = $"/api/documents/{document.Id}/download";
                        dto.PreviewUrl = GetPreviewUrl(document);

                        // Set permissions
                        dto.CanEdit = document.UploadedByUserId == _currentUserService.UserId || isAdmin;
                        dto.CanDelete = document.UploadedByUserId == _currentUserService.UserId || isAdmin;
                        dto.CanDownload = CanUserDownloadDocument(document, _currentUserService.UserId ?? 1, isAdmin);
                    }
                }

                return documentList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while retrieving documents for user: {_currentUserService.UserId}");
                return PaginatedResult<DocumentDto>.ServerError(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Apply sorting to documents query
        /// </summary>
        private IQueryable<Document> ApplySorting(IQueryable<Document> query, string? sortBy, string? sortDirection)
        {
            var isDescending = sortDirection?.Equals("desc", StringComparison.OrdinalIgnoreCase) ?? false;

            return sortBy?.ToLowerInvariant() switch
            {
                "name" => isDescending ? query.OrderByDescending(d => d.Name) : query.OrderBy(d => d.Name),
                "filename" => isDescending ? query.OrderByDescending(d => d.Attachment.FileName) : query.OrderBy(d => d.Attachment.FileName),
                "filesize" => isDescending ? query.OrderByDescending(d => d.Attachment.FileSize) : query.OrderBy(d => d.Attachment.FileSize),
                "downloadcount" => isDescending ? query.OrderByDescending(d => d.DownloadCount) : query.OrderBy(d => d.DownloadCount),
                "createdat" => isDescending ? query.OrderByDescending(d => d.CreatedAt) : query.OrderBy(d => d.CreatedAt),
                _ => query.OrderByDescending(d => d.CreatedAt)
            };
        }

        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Get localized category name
        /// </summary>
        private string GetLocalizedCategoryName(DocumentCategory category)
        {
            var culture = CultureInfo.CurrentCulture.Name;
            return culture.StartsWith("ar") ? category.NameAr : category.NameEn;
        }

        /// <summary>
        /// Get access level display name
        /// </summary>
        private string GetAccessLevelName(DocumentAccessLevel accessLevel)
        {
            return accessLevel switch
            {
                DocumentAccessLevel.Public => _localizer["Public"],
                DocumentAccessLevel.Private => _localizer["Private"],
                DocumentAccessLevel.Restricted => _localizer["Restricted"],
                _ => _localizer["Unknown"]
            };
        }

        /// <summary>
        /// Get preview URL for supported file types
        /// </summary>
        private string? GetPreviewUrl(Document document)
        {
            var fileExtension = Path.GetExtension(document.Attachment.FileName).ToLowerInvariant();
            var supportedPreviewTypes = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            if (supportedPreviewTypes.Contains(fileExtension))
            {
                return $"/api/documents/{document.Id}/preview";
            }
            return null;
        }

        /// <summary>
        /// Check if user can download document
        /// </summary>
        private bool CanUserDownloadDocument(Document document, int userId, bool isAdmin)
        {
            return document.AccessLevel switch
            {
                DocumentAccessLevel.Public => true,
                DocumentAccessLevel.Private => document.UploadedByUserId == userId || isAdmin,
                DocumentAccessLevel.Restricted => document.UploadedByUserId == userId || isAdmin,
                _ => false
            };
        }

        #endregion
    }
}

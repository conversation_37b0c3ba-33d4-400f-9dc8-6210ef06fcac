using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;

namespace Application.Features.DocumentManagement.Queries.GetDocuments
{
    /// <summary>
    /// Query for getting documents with filtering and pagination
    /// </summary>
    public class GetDocumentsQuery : IQuery<BaseResponse<PaginatedResult<DocumentDto>>>
    {
        /// <summary>
        /// Document category ID filter (optional)
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Search term for document name/description
        /// </summary>
        public string? SearchTerm { get; set; }

    }
}

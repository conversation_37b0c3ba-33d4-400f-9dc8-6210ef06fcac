using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;

namespace Application.Features.DocumentManagement.Queries.GetDocuments
{
    /// <summary>
    /// Query for getting documents with filtering and pagination
    /// </summary>
    public class GetDocumentsQuery : IQuery<BaseResponse<PaginatedResult<DocumentDto>>>
    {
        /// <summary>
        /// Document category ID filter (optional)
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Search term for document name/description
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// File extension filter (e.g., ".pdf", ".docx")
        /// </summary>
        public string? FileExtension { get; set; }

        /// <summary>
        /// Access level filter
        /// </summary>
        public int? AccessLevel { get; set; }

        /// <summary>
        /// Filter by uploader user ID
        /// </summary>
        public int? UploadedByUserId { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; } = true;

        /// <summary>
        /// Date range filter - from date
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Date range filter - to date
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Tags filter (comma-separated)
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Sort field (Name, CreatedAt, FileSize, DownloadCount)
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// Sort direction (asc, desc)
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Paginated result wrapper
    /// </summary>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// List of items
        /// </summary>
        public List<T> Items { get; set; } = new();

        /// <summary>
        /// Total count of items
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPreviousPage { get; set; }

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNextPage { get; set; }
    }
}

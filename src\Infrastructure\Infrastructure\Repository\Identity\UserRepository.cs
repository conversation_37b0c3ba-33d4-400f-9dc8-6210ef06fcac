using Abstraction.Contract.Repository.Identity;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Domain.Entities.Users;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.Identity
{
    /// <summary>
    /// Repository implementation for User entity operations
    /// </summary>
    public class UserRepository : GenericRepository, IUserRepository
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        #endregion

        #region Constructor
        public UserRepository(
            AppDbContext repositoryContext, 
            ICurrentUserService currentUserService,
            IIdentityServiceManager identityServiceManager)
            : base(repositoryContext, currentUserService)
        {
            _identityServiceManager = identityServiceManager;
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get user by ID
        /// </summary>
        public async Task<User?> GetByIdAsync(int userId)
        {
            return await _identityServiceManager.UserManagmentService.FindByIdAsync(userId.ToString());
        }

        /// <summary>
        /// Get user roles by user ID
        /// </summary>
        public async Task<List<string>> GetUserRolesAsync(int userId)
        {
            var user = await GetByIdAsync(userId);
            if (user == null) return new List<string>();

            var roles = await _identityServiceManager.UserManagmentService.GetUserRolesAsync(user);
            return roles.ToList();
        }

        /// <summary>
        /// Get user by email
        /// </summary>
        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _identityServiceManager.UserManagmentService.FindByEmailAsync(email);
        }

        /// <summary>
        /// Get user by username
        /// </summary>
        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _identityServiceManager.UserManagmentService.FindByNameAsync(username);
        }

        /// <summary>
        /// Get users by role
        /// </summary>
        public async Task<List<User>> GetUsersByRoleAsync(string roleName)
        {
            var users = await _identityServiceManager.UserManagmentService.GetUsersByRole(roleName);
            return users.ToList();
        }

        /// <summary>
        /// Check if user exists
        /// </summary>
        public async Task<bool> UserExistsAsync(int userId)
        {
            var user = await GetByIdAsync(userId);
            return user != null;
        }

        /// <summary>
        /// Check if user has role
        /// </summary>
        public async Task<bool> UserHasRoleAsync(int userId, string roleName)
        {
            var userRoles = await GetUserRolesAsync(userId);
            return userRoles.Any(r => r.Equals(roleName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get active users
        /// </summary>
        public async Task<List<User>> GetActiveUsersAsync()
        {
            return await RepositoryContext.Set<User>()
                .Where(u => u.IsActive)
                .ToListAsync();
        }

        /// <summary>
        /// Get user with roles
        /// </summary>
        public async Task<User?> GetUserWithRolesAsync(int userId)
        {
            // Use the enhanced method if available
            var user = await _identityServiceManager.UserManagmentService.FindByIdWithRolesAsync(userId.ToString());
            return user;
        }

        /// <summary>
        /// Search users by name or email
        /// </summary>
        public async Task<List<User>> SearchUsersAsync(string searchTerm)
        {
            var lowerSearchTerm = searchTerm.ToLower();
            return await RepositoryContext.Set<User>()
                .Where(u => u.IsActive && (
                    u.FullName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm) ||
                    u.UserName.ToLower().Contains(lowerSearchTerm)))
                .ToListAsync();
        }

        #endregion
    }
}

using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.DocumentManagement.Dtos
{
    /// <summary>
    /// DTO for uploading documents
    /// </summary>
    public class UploadDocumentDto
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        [Required(ErrorMessage = "Document name is required")]
        [StringLength(255, ErrorMessage = "Document name cannot exceed 255 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Document category ID
        /// </summary>
        [Required(ErrorMessage = "Document category is required")]
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Document description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Document tags (comma-separated)
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// Access level
        /// </summary>
        public int AccessLevel { get; set; } = 2; // Default to Private

        /// <summary>
        /// File to upload
        /// </summary>
        [Required(ErrorMessage = "File is required")]
        public IFormFile File { get; set; } = null!;
    }

    /// <summary>
    /// DTO for document upload response
    /// </summary>
    public class DocumentUploadResponseDto
    {
        /// <summary>
        /// Uploaded document ID
        /// </summary>
        public int DocumentId { get; set; }

        /// <summary>
        /// Document name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// File name
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File size
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Formatted file size
        /// </summary>
        public string FormattedFileSize { get; set; } = string.Empty;

        /// <summary>
        /// Upload success message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Download URL
        /// </summary>
        public string? DownloadUrl { get; set; }
    }
}

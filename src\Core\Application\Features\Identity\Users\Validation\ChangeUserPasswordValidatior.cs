﻿using FluentValidation;
using Application.Features.Identity.Users.Commands.ChangePassword;

namespace Application.Features.Identity.Users.Validation
{
    public class ChangeUserPasswordValidatior : AbstractValidator<ChangePasswordCommand>
    {
        #region Constructors
        public ChangeUserPasswordValidatior()
        {
            ApplyValidationsRules();
        }
        #endregion

        #region Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.Id)
                .NotNull().WithMessage("Can't be blank.");

            RuleFor(x => x.CurrentPassword)
               .NotNull().WithMessage("Can't be blank.");

            RuleFor(x => x.NewPassword)
               .NotNull().WithMessage("Can't be blank.");

            RuleFor(x => x.ConfirmPassword)
               .NotNull().WithMessage("Can't be blank.")
               .Equal(x => x.NewPassword).WithMessage("newPassword and confirmPassword not Equals.");


        }
        #endregion
    }
}

using Abstraction.Base.Response;
using Application.Base.Abstracts;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.DocumentManagement.Queries.DownloadDocument
{
    /// <summary>
    /// Query for downloading a document
    /// </summary>
    public class DownloadDocumentQuery : IQuery<BaseResponse<DocumentDownloadResult>>
    {
        /// <summary>
        /// Document ID to download
        /// </summary>
        [Required(ErrorMessage = "Document ID is required")]
        public int DocumentId { get; set; }

        /// <summary>
        /// Whether this is a preview request (doesn't increment download count)
        /// </summary>
        public bool IsPreview { get; set; } = false;
    }

    /// <summary>
    /// Document download result
    /// </summary>
    public class DocumentDownloadResult
    {
        /// <summary>
        /// File content as byte array
        /// </summary>
        public byte[] FileContent { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// File name for download
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Content type
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// File size
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Whether file exists
        /// </summary>
        public bool FileExists { get; set; }

        /// <summary>
        /// Error message if any
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}

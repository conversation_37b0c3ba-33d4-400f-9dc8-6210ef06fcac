using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.Fund;
using Domain.Entities.FundManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.Fund
{
    /// <summary>
    /// Repository implementation for BoardMember entity operations
    /// Inherits from GenericRepository and implements IBoardMemberRepository
    /// Provides specific methods for board member business logic
    /// </summary>
    public class BoardMemberRepository : GenericRepository, IBoardMemberRepository
    {
        #region Constructor
        
        public BoardMemberRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        
        #endregion
        
        #region IBoardMemberRepository Implementation
        
        /// <summary>
        /// Gets all active board members for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of active board members</returns>
        public async Task<IEnumerable<BoardMember>> GetActiveBoardMembersByFundIdAsync(int fundId, bool trackChanges = false)
        {
            var query = GetByCondition<BoardMember>(
                bm => bm.FundId == fundId && bm.IsActive, 
                trackChanges);
                
            return await query
                .Include(bm => bm.User)
                .Include(bm => bm.Fund)
                .OrderByDescending(bm => bm.UpdatedAt ?? bm.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Gets the count of active independent board members for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <returns>Count of active independent board members</returns>
        public async Task<int> GetActiveIndependentMemberCountAsync(int fundId)
        {
            return await GetByCondition<BoardMember>(
                bm => bm.FundId == fundId &&
                      bm.IsActive &&
                      bm.MemberType == BoardMemberType.Independent,
                trackChanges: false)
                .CountAsync();
        }

        /// <summary>
        /// Gets the count of active independent board members for a specific fund
        /// Alternative method name for consistency with AddBoardMemberHandler usage
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <returns>Count of active independent board members</returns>
        public async Task<int> IndependentMembersCountAsync(int fundId)
        {
            return await GetActiveIndependentMemberCountAsync(fundId);
        }

        /// <summary>
        /// Gets the current chairman for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Chairman board member or null if none exists</returns>
        public async Task<BoardMember?> GetChairmanByFundIdAsync(int fundId, bool trackChanges = false)
        {
            var query = GetByCondition<BoardMember>(
                bm => bm.FundId == fundId && 
                      bm.IsActive && 
                      bm.IsChairman, 
                trackChanges);
                
            return await query
                .Include(bm => bm.User)
                .Include(bm => bm.Fund)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Checks if a user is already a board member of a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="userId">User identifier</param>
        /// <returns>True if user is already a board member, false otherwise</returns>
        public async Task<bool> IsUserBoardMemberAsync(int fundId, int userId)
        {
            return await GetByCondition<BoardMember>(
                bm => bm.FundId == fundId && 
                      bm.UserId == userId && 
                      bm.IsActive,
                trackChanges: false)
                .AnyAsync();
        }

        /// <summary>
        /// Gets board members by type for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="memberType">Type of board member</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of board members of specified type</returns>
        public   IQueryable<BoardMember> GetBoardMembersByTypeAsync(int fundId, bool trackChanges = false)
        {
            var query = GetByCondition<BoardMember>(
                bm => bm.FundId == fundId ,
                trackChanges);

            return query
                .Include(bm => bm.User)
                .Include(bm => bm.Fund)
                .OrderByDescending(bm => bm.UpdatedAt ?? bm.CreatedAt);
                
        }
        
        #endregion
    }
}

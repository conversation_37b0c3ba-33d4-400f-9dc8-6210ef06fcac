using Domain.Entities.FundManagement;

namespace Abstraction.Contracts.Repository.Fund
{
    /// <summary>
    /// Repository interface for BoardMember entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for board member business logic
    /// </summary>
    public interface IBoardMemberRepository : IGenericRepository
    {
        /// <summary>
        /// Gets all active board members for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of active board members</returns>
        Task<IEnumerable<BoardMember>> GetActiveBoardMembersByFundIdAsync(int fundId, bool trackChanges = false);

        /// <summary>
        /// Gets the count of active independent board members for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <returns>Count of active independent board members</returns>
        Task<int> GetActiveIndependentMemberCountAsync(int fundId);

        /// <summary>
        /// Gets the count of active independent board members for a specific fund
        /// Alternative method name for consistency with AddBoardMemberHandler usage
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <returns>Count of active independent board members</returns>
        Task<int> IndependentMembersCountAsync(int fundId);

        /// <summary>
        /// Gets the current chairman for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Chairman board member or null if none exists</returns>
        Task<BoardMember?> GetChairmanByFundIdAsync(int fundId, bool trackChanges = false);

        /// <summary>
        /// Checks if a user is already a board member of a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="userId">User identifier</param>
        /// <returns>True if user is already a board member, false otherwise</returns>
        Task<bool> IsUserBoardMemberAsync(int fundId, int userId);

        /// <summary>
        /// Gets board members by type for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="memberType">Type of board member</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of board members of specified type</returns>
        IQueryable<BoardMember> GetBoardMembersByTypeAsync(int fundId,  bool trackChanges = false);
    }
}

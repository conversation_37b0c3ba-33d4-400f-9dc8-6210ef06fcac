namespace Abstraction.Enums
{
    /// <summary>
    /// Enumeration of WhatsApp message types for different notification scenarios
    /// Based on Sprint 3 requirements for user management notifications
    /// </summary>
    public enum WhatsAppMessageType
    {
        /// <summary>
        /// Password reset notification message
        /// Sent when admin resets a user's password
        /// Message ID: MSG-RESET-006
        /// </summary>
        PasswordReset = 1,

        /// <summary>
        /// User registration notification message
        /// Sent when a new user is added to the system
        /// Message ID: MSG-ADD-008
        /// </summary>
        UserRegistration = 2,

        /// <summary>
        /// Account activation notification message
        /// Sent when a user account is activated
        /// Message ID: MSG-ACTDEACT-009
        /// </summary>
        AccountActivation = 3,

        /// <summary>
        /// Account deactivation notification message
        /// Sent when a user account is deactivated
        /// Message ID: MSG-ACTDEACT-010
        /// </summary>
        AccountDeactivation = 4,

        /// <summary>
        /// Registration message resend notification
        /// Sent when registration message is resent to user
        /// Message ID: MSG-ADD-008 (Resend)
        /// </summary>
        RegistrationMessageResend = 5,

        /// <summary>
        /// Fund member added notification
        /// Sent when a user is added as a fund member
        /// Custom message type for fund management
        /// </summary>
        FundMemberAdded = 6
    }

    /// <summary>
    /// Enumeration of message priority levels for WhatsApp delivery
    /// Higher priority messages may be processed first
    /// </summary>
    public enum MessagePriority
    {
        /// <summary>
        /// Low priority message
        /// Non-urgent notifications
        /// </summary>
        Low = 1,

        /// <summary>
        /// Normal priority message
        /// Standard notifications (default)
        /// </summary>
        Normal = 2,

        /// <summary>
        /// High priority message
        /// Important notifications that should be delivered quickly
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical priority message
        /// Urgent notifications requiring immediate delivery
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// Enumeration of WhatsApp message delivery statuses
    /// Tracks the lifecycle of a WhatsApp message from sending to delivery
    /// </summary>
    public enum WhatsAppDeliveryStatus
    {
        /// <summary>
        /// Message is queued for sending
        /// Initial status when message is created
        /// </summary>
        Queued = 1,

        /// <summary>
        /// Message has been sent to WhatsApp API
        /// Successfully submitted to WhatsApp servers
        /// </summary>
        Sent = 2,

        /// <summary>
        /// Message has been delivered to recipient's device
        /// Confirmed delivery by WhatsApp
        /// </summary>
        Delivered = 3,

        /// <summary>
        /// Message has been read by the recipient
        /// Read receipt received (if enabled)
        /// </summary>
        Read = 4,

        /// <summary>
        /// Message delivery failed
        /// Could not be delivered due to various reasons
        /// </summary>
        Failed = 5,

        /// <summary>
        /// Message was rejected by WhatsApp
        /// Invalid phone number, blocked user, etc.
        /// </summary>
        Rejected = 6,

        /// <summary>
        /// Message delivery is pending
        /// Temporary status during processing
        /// </summary>
        Pending = 7,

        /// <summary>
        /// Message delivery timed out
        /// No response received within timeout period
        /// </summary>
        Timeout = 8
    }
}

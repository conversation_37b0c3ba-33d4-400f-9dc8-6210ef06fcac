using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Domain.Entities.DocumentManagement;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.DocumentManagement.Commands.UploadDocument
{
    /// <summary>
    /// Handler for uploading documents
    /// </summary>
    public class UploadDocumentCommandHandler : BaseResponseHandler, ICommandHandler<UploadDocumentCommand, BaseResponse<DocumentUploadResponseDto>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public UploadDocumentCommandHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<DocumentUploadResponseDto>> Handle(UploadDocumentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document upload for user {_currentUserService.UserId}");

                // Validate category exists
                var category = await _repositoryManager.DocumentCategoryRepository.GetByIdAsync<DocumentCategory>(request.DocumentCategoryId, false);
                if (category == null)
                {
                    return BadRequest<DocumentUploadResponseDto>(_localizer["DocumentCategoryNotFound"]);
                }

                // Validate file
                if (request.File == null || request.File.Length == 0)
                {
                    return BadRequest<DocumentUploadResponseDto>(_localizer["FileIsRequired"]);
                }

                // Check file size limits
                if (category.MaxFileSize.HasValue && request.File.Length > category.MaxFileSize.Value)
                {
                    return BadRequest<DocumentUploadResponseDto>(
                        string.Format(_localizer["FileSizeExceedsLimit"], FormatFileSize(category.MaxFileSize.Value)));
                }

                // Check allowed extensions
                var fileExtension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                if (!string.IsNullOrEmpty(category.AllowedExtensions))
                {
                    var allowedExtensions = category.AllowedExtensions.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(ext => ext.Trim().ToLowerInvariant()).ToList();
                    
                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        return BadRequest<DocumentUploadResponseDto>(
                            string.Format(_localizer["FileExtensionNotAllowed"], category.AllowedExtensions));
                    }
                }

                // Generate unique file name
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine("uploads", "documents", category.Id.ToString());
                var fullUploadPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", uploadPath);

                // Ensure directory exists
                Directory.CreateDirectory(fullUploadPath);

                var filePath = Path.Combine(fullUploadPath, uniqueFileName);
                var relativePath = Path.Combine(uploadPath, uniqueFileName).Replace("\\", "/");

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await request.File.CopyToAsync(stream, cancellationToken);
                }

                // Create document entity
                var document = new Document
                {
                    Name = request.Name,
                    FileName = request.File.FileName,
                    FilePath = relativePath,
                    FileSize = request.File.Length,
                    ContentType = request.File.ContentType,
                    FileExtension = fileExtension,
                    DocumentCategoryId = request.DocumentCategoryId,
                    UploadedByUserId = _currentUserService.UserId ?? 1,
                    Description = request.Description,
                    Tags = request.Tags,
                    AccessLevel = (DocumentAccessLevel)request.AccessLevel,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = _currentUserService.UserId ?? 1
                };

                // Save to database
                await _repositoryManager.DocumentRepository.AddAsync(document);
                await _repositoryManager.SaveAsync();

                _logger.LogInfo($"Document uploaded successfully. ID: {document.Id}, Name: {document.Name}");

                // Prepare response
                var response = new DocumentUploadResponseDto
                {
                    DocumentId = document.Id,
                    Name = document.Name,
                    FileName = document.FileName,
                    FileSize = document.FileSize,
                    FormattedFileSize = FormatFileSize(document.FileSize),
                    Message = _localizer["DocumentUploadedSuccessfully"],
                    DownloadUrl = $"/api/documents/{document.Id}/download"
                };

                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading document: {ex.Message}");
                return ServerError<DocumentUploadResponseDto>(_localizer["DocumentUploadFailed"]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        #endregion
    }
}

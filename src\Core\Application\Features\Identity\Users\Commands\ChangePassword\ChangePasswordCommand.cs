﻿using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Users.Commands.ChangePassword
{
    /// <summary>
    /// Command for changing user password
    /// Enhanced for Sprint 3 with registration completion logic
    /// </summary>
    public record ChangePasswordCommand : ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// User ID (set automatically from current user context)
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Current password (optional for first-time users)
        /// </summary>
        public string? CurrentPassword { get; set; }

        /// <summary>
        /// New password
        /// </summary>
        public string NewPassword { get; set; } = null!;

        /// <summary>
        /// Confirm new password
        /// </summary>
        public string ConfirmPassword { get; set; } = null!;

        /// <summary>
        /// Indicates if this is a mandatory first-time password change
        /// </summary>
        public bool IsMandatoryReset { get; set; } = false;
    }

    
}

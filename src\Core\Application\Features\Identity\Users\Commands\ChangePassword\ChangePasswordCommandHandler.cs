﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;

namespace Application.Features.Identity.Users.Commands.ChangePassword
{
    /// <summary>
    /// Handler for changing user password
    /// Enhanced for Sprint 3 with registration completion logic
    /// </summary>
    public class ChangePasswordCommandHandler : BaseResponseHandler, ICommandHandler<ChangePasswordCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public ChangePasswordCommandHandler(
            IIdentityServiceManager service,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            IIdentityServiceManager identityServiceManager,
            ICurrentUserService currentUserService)
        {
            _mapper = mapper;
            _service = service;
            _localizer = localizer;
            _currentUserService = currentUserService;
            _identityServiceManager = identityServiceManager;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Set user ID from current user context if not provided
                if (request.Id == 0)
                {
                    request.Id = _currentUserService.UserId.GetValueOrDefault();
                }

                // Get user
                var user = await _identityServiceManager.UserManagmentService.FindByIdAsync(request.Id.ToString());
                if (user == null)
                    return NotFound<string>(_localizer[SharedResourcesKey.UserNotFound]);

                // Authorization check - users can only change their own password unless they're admin
                var currentUserId = _currentUserService.UserId.GetValueOrDefault();
                if (request.Id != currentUserId)
                {
                    var currentUser = await _identityServiceManager.UserManagmentService.FindByIdAsync(currentUserId.ToString());
                    if (currentUser == null)
                    {
                        return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                    }

                    var currentUserRoles = await _identityServiceManager.AuthorizationService.GetUsersRoles(currentUser);
                    if (!currentUserRoles.UserRoles.Any(role => role.Name.Contains("Admin")) && !currentUserRoles.UserRoles.Any(role => role.Name.Contains("SuperAdmin")))
                    {
                        return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedUserAccess]);
                    }
                }

                // Sprint 3 Enhancement: Handle first-time password change
                bool isFirstTimePasswordChange = !user.RegistrationIsCompleted;
                IdentityResult result;
                if (isFirstTimePasswordChange || request.IsMandatoryReset)
                {
                    // For first-time users or mandatory resets, we don't require current password
                    // Remove existing password and add new one
                    if (await _identityServiceManager.AuthenticationService.HasPasswordAsync(user))
                    {
                        await _identityServiceManager.AuthenticationService.RemovePasswordAsync(user);
                    }
                    result = await _identityServiceManager.AuthenticationService.AddPasswordAsync(user, request.NewPassword);
                }
                else
                {
                    // Regular password change - requires current password
                    if (string.IsNullOrEmpty(request.CurrentPassword))
                    {
                        return BadRequest<string>(_localizer[SharedResourcesKey.PasswordIncorrectCurrent]);
                    }
                    result = await _identityServiceManager.AuthenticationService.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
                }

                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.PasswordChangeSystemError]}: {errors}");
                }

                // Sprint 3 Enhancement: Mark registration as completed for first-time users
                if (isFirstTimePasswordChange)
                {
                    user.RegistrationIsCompleted = true;
                    user.UpdatedAt = DateTime.UtcNow;
                    user.UpdatedBy = currentUserId;
                    await _identityServiceManager.UserManagmentService.UpdateAsync(user);
                }
                return Success<string>(_localizer[SharedResourcesKey.PasswordChangedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.PasswordChangeSystemError]);
            }
        }
        #endregion
    }
}

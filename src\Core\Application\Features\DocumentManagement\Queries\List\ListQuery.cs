using Abstraction.Base.Dto;
using Abstraction.Common.Wappers;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Domain.Entities.DocumentManagement;

namespace Application.Features.DocumentManagement.Queries.List
{
    /// <summary>
    /// Query for retrieving documents with filtering and pagination
    /// Implements CQRS pattern using MediatR
    /// Follows BoardMembers ListQuery pattern for consistency
    /// </summary>
    public record ListQuery : BaseListDto, IQuery<PaginatedResult<DocumentDto>>
    {
        /// <summary>
        /// Document category ID filter (optional)
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// File extension filter (optional)
        /// </summary>
        public string? FileExtension { get; set; }

        /// <summary>
        /// Access level filter (optional)
        /// </summary>
        public DocumentAccessLevel? AccessLevel { get; set; }

        /// <summary>
        /// Uploaded by user ID filter (optional)
        /// </summary>
        public int? UploadedByUserId { get; set; }

        /// <summary>
        /// Active status filter (optional)
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// From date filter for creation date (optional)
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// To date filter for creation date (optional)
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Tags filter (optional)
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Sort by field (optional)
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort direction (asc/desc, optional)
        /// </summary>
        public string? SortDirection { get; set; }


    }
}

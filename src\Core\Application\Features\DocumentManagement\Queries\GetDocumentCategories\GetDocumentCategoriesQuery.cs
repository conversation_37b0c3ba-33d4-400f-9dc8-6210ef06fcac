using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;

namespace Application.Features.DocumentManagement.Queries.GetDocumentCategories
{
    /// <summary>
    /// Query for getting document categories
    /// </summary>
    public class GetDocumentCategoriesQuery : IQuery<BaseResponse<List<DocumentCategoryDto>>>
    {
        /// <summary>
        /// Whether to include inactive categories
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Whether to include document counts
        /// </summary>
        public bool IncludeDocumentCounts { get; set; } = true;

        /// <summary>
        /// Whether to include child categories
        /// </summary>
        public bool IncludeChildCategories { get; set; } = true;

        /// <summary>
        /// Parent category ID filter (null for root categories)
        /// </summary>
        public int? ParentCategoryId { get; set; }
    }
}

using Xunit;
using Moq;
using Microsoft.Extensions.Localization;
using AutoMapper;
using Application.Features.DocumentManagement.Commands.UploadDocument;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Resources;
using Domain.Entities.DocumentManagement;
using Microsoft.AspNetCore.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Abstraction.Base.Response;

namespace Tests.Core.Application.UnitTests.Features.DocumentManagement
{
    /// <summary>
    /// Unit tests for UploadDocumentCommandHandler
    /// Tests the document upload functionality
    /// </summary>
    public class UploadDocumentCommandHandlerTests
    {
        private readonly Mock<IRepositoryManager> _mockRepositoryManager;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly UploadDocumentCommandHandler _handler;

        public UploadDocumentCommandHandlerTests()
        {
            _mockRepositoryManager = new Mock<IRepositoryManager>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();
            _mockLogger = new Mock<ILoggerManager>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();

            _handler = new UploadDocumentCommandHandler(
                _mockRepositoryManager.Object,
                _mockCurrentUserService.Object,
                _mockLogger.Object,
                _mockLocalizer.Object);
        }

        [Fact]
        public async Task Handle_ValidDocument_ShouldUploadSuccessfully()
        {
            // Arrange
            var category = new DocumentCategory
            {
                Id = 1,
                NameEn = "Test Category",
                NameAr = "فئة اختبار",
                MaxFileSize = 10 * 1024 * 1024, // 10 MB
                AllowedExtensions = ".pdf,.doc,.docx"
            };

            var mockFile = CreateMockFile("test.pdf", "application/pdf", "Test content");
            var command = new UploadDocumentCommand
            {
                Name = "Test Document",
                DocumentCategoryId = 1,
                Description = "Test description",
                Tags = "test,document",
                AccessLevel = 2, // Private
                File = mockFile
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(1);
            _mockRepositoryManager.Setup(x => x.DocumentCategoryRepository.GetByIdAsync(1))
                .ReturnsAsync(category);
            _mockRepositoryManager.Setup(x => x.DocumentRepository.AddAsync(It.IsAny<Document>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
            _mockRepositoryManager.Setup(x => x.SaveAsync())
                .Returns(Task.CompletedTask);

            SetupLocalizer("DocumentUploadedSuccessfully", "Document uploaded successfully");

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Succeeded);
            Assert.NotNull(result.Data);
            Assert.Equal("Test Document", result.Data.Name);
            Assert.Equal("test.pdf", result.Data.FileName);
            
            _mockRepositoryManager.Verify(x => x.DocumentRepository.AddAsync(It.IsAny<Document>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockRepositoryManager.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_CategoryNotFound_ShouldReturnBadRequest()
        {
            // Arrange
            var mockFile = CreateMockFile("test.pdf", "application/pdf", "Test content");
            var command = new UploadDocumentCommand
            {
                Name = "Test Document",
                DocumentCategoryId = 999, // Non-existent category
                File = mockFile
            };

            _mockRepositoryManager.Setup(x => x.DocumentCategoryRepository.GetByIdAsync(999))
                .ReturnsAsync((DocumentCategory)null);

            SetupLocalizer("DocumentCategoryNotFound", "Document category not found");

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("Document category not found", result.Message);
        }

        [Fact]
        public async Task Handle_FileSizeExceedsLimit_ShouldReturnBadRequest()
        {
            // Arrange
            var category = new DocumentCategory
            {
                Id = 1,
                NameEn = "Test Category",
                MaxFileSize = 1024, // 1 KB limit
                AllowedExtensions = ".pdf"
            };

            var mockFile = CreateMockFile("large.pdf", "application/pdf", new string('x', 2048)); // 2 KB file
            var command = new UploadDocumentCommand
            {
                Name = "Large Document",
                DocumentCategoryId = 1,
                File = mockFile
            };

            _mockRepositoryManager.Setup(x => x.DocumentCategoryRepository.GetByIdAsync(1))
                .ReturnsAsync(category);

            SetupLocalizer("FileSizeExceedsLimit", "File size exceeds limit of {0}");

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Contains("File size exceeds limit", result.Message);
        }

        [Fact]
        public async Task Handle_InvalidFileExtension_ShouldReturnBadRequest()
        {
            // Arrange
            var category = new DocumentCategory
            {
                Id = 1,
                NameEn = "Test Category",
                MaxFileSize = 10 * 1024 * 1024,
                AllowedExtensions = ".pdf,.doc" // Only PDF and DOC allowed
            };

            var mockFile = CreateMockFile("test.txt", "text/plain", "Test content"); // TXT not allowed
            var command = new UploadDocumentCommand
            {
                Name = "Text Document",
                DocumentCategoryId = 1,
                File = mockFile
            };

            _mockRepositoryManager.Setup(x => x.DocumentCategoryRepository.GetByIdAsync(1))
                .ReturnsAsync(category);

            SetupLocalizer("FileExtensionNotAllowed", "File extension not allowed. Allowed extensions: {0}");

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Contains("File extension not allowed", result.Message);
        }

        [Fact]
        public async Task Handle_EmptyFile_ShouldReturnBadRequest()
        {
            // Arrange
            var category = new DocumentCategory
            {
                Id = 1,
                NameEn = "Test Category",
                MaxFileSize = 10 * 1024 * 1024,
                AllowedExtensions = ".pdf"
            };

            var mockFile = CreateMockFile("empty.pdf", "application/pdf", ""); // Empty file
            var command = new UploadDocumentCommand
            {
                Name = "Empty Document",
                DocumentCategoryId = 1,
                File = mockFile
            };

            _mockRepositoryManager.Setup(x => x.DocumentCategoryRepository.GetByIdAsync(1))
                .ReturnsAsync(category);

            SetupLocalizer("FileIsRequired", "File is required");

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("File is required", result.Message);
        }

        #region Helper Methods

        private IFormFile CreateMockFile(string fileName, string contentType, string content)
        {
            var bytes = Encoding.UTF8.GetBytes(content);
            var stream = new MemoryStream(bytes);
            
            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns(fileName);
            mockFile.Setup(f => f.ContentType).Returns(contentType);
            mockFile.Setup(f => f.Length).Returns(bytes.Length);
            mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
            mockFile.Setup(f => f.CopyToAsync(It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                .Returns((Stream target, CancellationToken token) =>
                {
                    stream.Position = 0;
                    return stream.CopyToAsync(target, token);
                });

            return mockFile.Object;
        }

        private void SetupLocalizer(string key, string value)
        {
            var localizedString = new LocalizedString(key, value);
            _mockLocalizer.Setup(x => x[key]).Returns(localizedString);
            _mockLocalizer.Setup(x => x[key, It.IsAny<object[]>()]).Returns(localizedString);
        }

        #endregion
    }
}

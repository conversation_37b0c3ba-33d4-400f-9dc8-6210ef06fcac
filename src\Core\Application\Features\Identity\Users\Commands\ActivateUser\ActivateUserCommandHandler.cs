using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Abstraction.Constants;

namespace Application.Features.Identity.Users.Commands.ActivateUser
{
    /// <summary>
    /// Handler for activating user accounts
    /// Implements Clean Architecture and CQRS patterns with audit logging
    /// </summary>
    public class ActivateDeActivateUserCommandHandler : BaseResponseHandler, ICommandHandler<ActivateUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly ICurrentUserService _currentUserService;

        #endregion

        #region Constructor
        public ActivateDeActivateUserCommandHandler(
            IStringLocalizer<SharedResources> localizer,
            IIdentityServiceManager identityServiceManager,
            ICurrentUserService currentUserService)
        {
            _localizer = localizer;
            _currentUserService = currentUserService;
            _identityServiceManager = identityServiceManager;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(ActivateUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the user to activate
                var user = await _identityServiceManager.UserManagmentService.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    return NotFound<string>(_localizer[SharedResourcesKey.UserNotFound]);
                }

                // Check if user is already active
                var isCurrentlyActive = request.Activate;
                // Activate the user by removing lockout
                user.LockoutEnabled = false;
                user.LockoutEnd = null;
                user.AccessFailedCount = 0; // Reset failed login attempts
                user.LastFailedLoginAttempt = null;  
                // Update audit fields
                user.UpdatedAt = DateTime.UtcNow;
                user.UpdatedBy = _currentUserService.UserId;
                user.IsActive = isCurrentlyActive;
                user.UpdatedBy = 2;
                // Save changes
                var result = await _identityServiceManager.UserManagmentService.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.SystemErrorSavingData]}: {errors}");
                }
                if (user.Roles.Select(c=>c.Name).Contains(RoleHelper.HeadOfRealEstate) || user.Roles.Select(c => c.Name).Contains(RoleHelper.FinanceController) || user.Roles.Select(c => c.Name).Contains(RoleHelper.ComplianceLegalManagingDirector))
                {
                    foreach (var role in user.Roles)
                    {
                        var updatedUser = await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(role.Name, user.Id);
                        updatedUser.IsActive = false;
                        await _identityServiceManager.UserManagmentService.UpdateAsync(updatedUser);
                    }
                }
                // TODO: Add audit logging when service is available
                // await _auditLogService.LogUserActionAsync(
                //     _currentUserService.UserId.GetValueOrDefault(),
                //     "User Activation",
                //     $"User {user.UserName} (ID: {user.Id}) was activated. Reason: {request.Reason ?? "Not specified"}",
                //     "Activation");

                // TODO: Send notification when service is available
                // if (request.SendNotification)
                // {
                //     await _notificationService.SendUserActivationNotificationAsync(user.Id, request.Reason);
                // }

                return Success<string>(isCurrentlyActive ? _localizer[SharedResourcesKey.UserActivatedSuccessfully] : _localizer[SharedResourcesKey.UserDeactivatedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion
    }
}

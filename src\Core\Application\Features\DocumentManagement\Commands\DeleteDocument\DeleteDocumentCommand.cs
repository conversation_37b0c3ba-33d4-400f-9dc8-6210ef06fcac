using Abstraction.Base.Response;
using Application.Base.Abstracts;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.DocumentManagement.Commands.DeleteDocument
{
    /// <summary>
    /// Command for deleting a document
    /// </summary>
    public class DeleteDocumentCommand : ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// Document ID to delete
        /// </summary>
        [Required(ErrorMessage = "Document ID is required")]
        public int DocumentId { get; set; }

        /// <summary>
        /// Whether to permanently delete the file from disk
        /// </summary>
        public bool PermanentDelete { get; set; } = false;
    }
}

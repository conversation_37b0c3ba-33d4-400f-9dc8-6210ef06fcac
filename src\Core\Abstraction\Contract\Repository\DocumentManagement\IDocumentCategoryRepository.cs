using Abstraction.Contracts.Repository;
using Domain.Entities.DocumentManagement;

namespace Abstraction.Contract.Repository.DocumentManagement
{
    /// <summary>
    /// Repository interface for DocumentCategory entity operations
    /// </summary>
    public interface IDocumentCategoryRepository : IGenericRepository
    {
        /// <summary>
        /// Get categories with optional filters
        /// </summary>
        Task<List<DocumentCategory>> GetCategoriesAsync(
            bool includeInactive = false,
            int? parentCategoryId = null,
            bool includeChildren = true);

        /// <summary>
        /// Get category hierarchy (tree structure)
        /// </summary>
        Task<List<DocumentCategory>> GetCategoryHierarchyAsync();

        /// <summary>
        /// Get root categories (categories without parent)
        /// </summary>
        Task<List<DocumentCategory>> GetRootCategoriesAsync(bool includeInactive = false);

        /// <summary>
        /// Get child categories of a parent category
        /// </summary>
        Task<List<DocumentCategory>> GetChildCategoriesAsync(int parentCategoryId, bool includeInactive = false);

        /// <summary>
        /// Get category with documents count
        /// </summary>
        Task<DocumentCategory?> GetCategoryWithDocumentCountAsync(int categoryId);

        /// <summary>
        /// Get categories by display order
        /// </summary>
        Task<List<DocumentCategory>> GetCategoriesByDisplayOrderAsync();

        /// <summary>
        /// Check if category has documents
        /// </summary>
        Task<bool> HasDocumentsAsync(int categoryId);

        /// <summary>
        /// Check if category has child categories
        /// </summary>
        Task<bool> HasChildCategoriesAsync(int categoryId);

        /// <summary>
        /// Get category path (breadcrumb)
        /// </summary>
        Task<List<DocumentCategory>> GetCategoryPathAsync(int categoryId);

        /// <summary>
        /// Search categories by name
        /// </summary>
        Task<List<DocumentCategory>> SearchCategoriesAsync(string searchTerm, string culture = "en");

        /// <summary>
        /// Get categories with document statistics
        /// </summary>
        Task<List<CategoryStatistics>> GetCategoriesWithStatisticsAsync();

        /// <summary>
        /// Reorder categories
        /// </summary>
        Task<bool> ReorderCategoriesAsync(Dictionary<int, int> categoryDisplayOrders);

        /// <summary>
        /// Check if category name exists
        /// </summary>
        Task<bool> CategoryNameExistsAsync(string nameAr, string nameEn, int? excludeCategoryId = null);

        /// <summary>
        /// Get category by name
        /// </summary>
        Task<DocumentCategory?> GetCategoryByNameAsync(string name, string culture = "en");
    }

    /// <summary>
    /// Category statistics model
    /// </summary>
    public class CategoryStatistics
    {
        public int CategoryId { get; set; }
        public string NameAr { get; set; } = string.Empty;
        public string NameEn { get; set; } = string.Empty;
        public int DocumentCount { get; set; }
        public long TotalFileSize { get; set; }
        public int TotalDownloads { get; set; }
        public DateTime? LastUploadDate { get; set; }
        public List<CategoryStatistics> ChildCategories { get; set; } = new();
    }
}

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Enumeration for document access levels
    /// </summary>
    public enum DocumentAccessLevel
    {
        /// <summary>
        /// Accessible to all users
        /// </summary>
        Public = 1,

        /// <summary>
        /// Accessible only to uploader and admins
        /// </summary>
        Private = 2,

        /// <summary>
        /// Accessible to specific roles/users
        /// </summary>
        Restricted = 3
    }
}

﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.Funds.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Abstraction.Contracts.Repository;
using Domain.Entities.FundManagement;


namespace Application.Features.Funds.Queries.Get
{
    public class GetQueryHandler : BaseResponseHandler, IQueryHandler<GetQuery, BaseResponse<GetFundResponse>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructor(s)
        public GetQueryHandler(IRepositoryManager repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<GetFundResponse>> Handle(GetQuery request, CancellationToken cancellationToken)
        {
            try
            {   
                var result = await _repository.Funds.EditFundById(request.Id, false);
                if (result == null)
                    return NotFound<GetFundResponse>("Fund with this Id not found!");
             var resultMapper = _mapper.Map<GetFundResponse>(result);
             return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<GetFundResponse>(ex.Message);
            }
        }

        #endregion
    }
}

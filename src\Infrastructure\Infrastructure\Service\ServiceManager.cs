﻿using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Abstraction.Contracts.Service.Catalog;
using AutoMapper;
using Infrastructure.Service.Catalog;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Service
{
    public class ServiceManager : IServiceManager
    {
        private readonly Lazy<IProductService> _productService;
        private readonly IGenericRepository _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ServiceManager(IGenericRepository repository, IMapper mapper, IStringLocalizer<SharedResources> localizer)
        {
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _productService = new Lazy<IProductService>(() => new ProductService(_repository, _mapper, _localizer));
 

        }
        public IProductService ProductService => _productService.Value;
 
    }
}

using Abstraction.Base.Dto;

namespace Application.Features.DocumentManagement.Dtos
{
    /// <summary>
    /// Document data transfer object
    /// </summary>
    public class DocumentDto : BaseDto
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Original file name
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Formatted file size (e.g., "1.5 MB")
        /// </summary>
        public string FormattedFileSize { get; set; } = string.Empty;

        /// <summary>
        /// MIME content type
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// File extension
        /// </summary>
        public string FileExtension { get; set; } = string.Empty;

        /// <summary>
        /// Document category ID
        /// </summary>
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Document category name
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// User who uploaded the document
        /// </summary>
        public int UploadedByUserId { get; set; }

        /// <summary>
        /// Name of user who uploaded
        /// </summary>
        public string UploadedByUserName { get; set; } = string.Empty;

        /// <summary>
        /// Document description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Document version number
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// Whether document is active/visible
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Document tags
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Access level
        /// </summary>
        public string AccessLevel { get; set; } = string.Empty;

        /// <summary>
        /// Download count
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// Upload date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last accessed date
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// Download URL
        /// </summary>
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Preview URL (for supported file types)
        /// </summary>
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// Whether current user can edit this document
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Whether current user can delete this document
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Whether current user can download this document
        /// </summary>
        public bool CanDownload { get; set; }
    }
}

using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Resources;
using System.Globalization;

namespace Application.Features.DocumentManagement.Queries.GetDocuments
{
    /// <summary>
    /// Handler for getting documents with filtering and pagination
    /// </summary>
    public class GetDocumentsQueryHandler : BaseResponseHandler, IQueryHandler<GetDocumentsQuery, BaseResponse<PaginatedResult<DocumentDto>>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public GetDocumentsQueryHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<PaginatedResult<DocumentDto>>> Handle(GetDocumentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting documents for user {_currentUserService.UserId} with filters");

                // Get user roles for permission checking
                var userRoles = await _repositoryManager.UserRepository.GetUserRolesAsync(_currentUserService.UserId);
                bool isAdmin = userRoles.Any(r => r.Equals("Admin", StringComparison.OrdinalIgnoreCase) || 
                                                r.Equals("Super Admin", StringComparison.OrdinalIgnoreCase));

                // Get documents with filters
                var documentsQuery = await _repositoryManager.DocumentRepository.GetDocumentsWithFiltersAsync(
                    categoryId: request.CategoryId,
                    searchTerm: request.SearchTerm,
                    fileExtension: request.FileExtension,
                    accessLevel: request.AccessLevel,
                    uploadedByUserId: request.UploadedByUserId,
                    isActive: request.IsActive,
                    fromDate: request.FromDate,
                    toDate: request.ToDate,
                    tags: request.Tags,
                    currentUserId: _currentUserService.UserId,
                    isAdmin: isAdmin);

                // Apply sorting
                documentsQuery = ApplySorting(documentsQuery, request.SortBy, request.SortDirection);

                // Get total count
                var totalCount = documentsQuery.Count();

                // Apply pagination
                var documents = documentsQuery
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // Map to DTOs
                var documentDtos = new List<DocumentDto>();
                foreach (var document in documents)
                {
                    var dto = _mapper.Map<DocumentDto>(document);
                    
                    // Set additional properties
                    dto.FormattedFileSize = FormatFileSize(document.FileSize);
                    dto.CategoryName = GetLocalizedCategoryName(document.DocumentCategory);
                    dto.UploadedByUserName = document.UploadedByUser?.FullName ?? "Unknown";
                    dto.AccessLevel = GetAccessLevelName(document.AccessLevel);
                    dto.DownloadUrl = $"/api/documents/{document.Id}/download";
                    dto.PreviewUrl = GetPreviewUrl(document);
                    
                    // Set permissions
                    dto.CanEdit = document.UploadedByUserId == _currentUserService.UserId || isAdmin;
                    dto.CanDelete = document.UploadedByUserId == _currentUserService.UserId || isAdmin;
                    dto.CanDownload = CanUserDownloadDocument(document, _currentUserService.UserId, isAdmin);

                    documentDtos.Add(dto);
                }

                // Create paginated result
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
                var result = new PaginatedResult<DocumentDto>
                {
                    Items = documentDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasPreviousPage = request.PageNumber > 1,
                    HasNextPage = request.PageNumber < totalPages
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting documents: {ex.Message}");
                return ServerError<PaginatedResult<DocumentDto>>(_localizer["ErrorGettingDocuments"]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Apply sorting to documents query
        /// </summary>
        private IQueryable<Domain.Entities.DocumentManagement.Document> ApplySorting(
            IQueryable<Domain.Entities.DocumentManagement.Document> query, 
            string sortBy, 
            string sortDirection)
        {
            var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

            return sortBy.ToLowerInvariant() switch
            {
                "name" => isDescending ? query.OrderByDescending(d => d.Name) : query.OrderBy(d => d.Name),
                "filename" => isDescending ? query.OrderByDescending(d => d.FileName) : query.OrderBy(d => d.FileName),
                "filesize" => isDescending ? query.OrderByDescending(d => d.FileSize) : query.OrderBy(d => d.FileSize),
                "downloadcount" => isDescending ? query.OrderByDescending(d => d.DownloadCount) : query.OrderBy(d => d.DownloadCount),
                "createdat" => isDescending ? query.OrderByDescending(d => d.CreatedAt) : query.OrderBy(d => d.CreatedAt),
                _ => query.OrderByDescending(d => d.CreatedAt)
            };
        }

        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Get localized category name
        /// </summary>
        private string GetLocalizedCategoryName(Domain.Entities.DocumentManagement.DocumentCategory category)
        {
            var culture = CultureInfo.CurrentCulture.Name;
            return culture.StartsWith("ar") ? category.NameAr : category.NameEn;
        }

        /// <summary>
        /// Get access level display name
        /// </summary>
        private string GetAccessLevelName(Domain.Entities.DocumentManagement.DocumentAccessLevel accessLevel)
        {
            return accessLevel switch
            {
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Public => _localizer["Public"],
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Private => _localizer["Private"],
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Restricted => _localizer["Restricted"],
                _ => _localizer["Unknown"]
            };
        }

        /// <summary>
        /// Get preview URL for supported file types
        /// </summary>
        private string? GetPreviewUrl(Domain.Entities.DocumentManagement.Document document)
        {
            var supportedPreviewTypes = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            if (supportedPreviewTypes.Contains(document.FileExtension.ToLowerInvariant()))
            {
                return $"/api/documents/{document.Id}/preview";
            }
            return null;
        }

        /// <summary>
        /// Check if user can download document
        /// </summary>
        private bool CanUserDownloadDocument(Domain.Entities.DocumentManagement.Document document, int userId, bool isAdmin)
        {
            return document.AccessLevel switch
            {
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Public => true,
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Private => document.UploadedByUserId == userId || isAdmin,
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Restricted => document.UploadedByUserId == userId || isAdmin,
                _ => false
            };
        }
        #endregion
    }
}

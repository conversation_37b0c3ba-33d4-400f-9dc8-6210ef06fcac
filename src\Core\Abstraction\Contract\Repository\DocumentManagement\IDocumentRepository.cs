using Abstraction.Contracts.Repository;
using Domain.Entities.DocumentManagement;

namespace Abstraction.Contract.Repository.DocumentManagement
{
    /// <summary>
    /// Repository interface for Document entity operations
    /// </summary>
    public interface IDocumentRepository : IGenericRepository
    {
        /// <summary>
        /// Get documents with filters and includes
        /// </summary>
        Task<IQueryable<Document>> GetDocumentsWithFiltersAsync(
            int? categoryId = null,
            string? searchTerm = null,
            string? fileExtension = null,
            int? accessLevel = null,
            int? uploadedByUserId = null,
            bool? isActive = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? tags = null,
            int currentUserId = 0,
            bool isAdmin = false);

        /// <summary>
        /// Get document count by category
        /// </summary>
        Task<int> GetDocumentCountByCategoryAsync(int categoryId);

        /// <summary>
        /// Get documents by category with pagination
        /// </summary>
        Task<IQueryable<Document>> GetDocumentsByCategoryAsync(int categoryId, bool includeInactive = false);

        /// <summary>
        /// Get recent documents for user
        /// </summary>
        Task<List<Document>> GetRecentDocumentsAsync(int userId, int count = 10);

        /// <summary>
        /// Get popular documents (by download count)
        /// </summary>
        Task<List<Document>> GetPopularDocumentsAsync(int count = 10);

        /// <summary>
        /// Search documents by text
        /// </summary>
        Task<IQueryable<Document>> SearchDocumentsAsync(string searchTerm, int? categoryId = null);

        /// <summary>
        /// Get documents by tags
        /// </summary>
        Task<IQueryable<Document>> GetDocumentsByTagsAsync(string[] tags);

        /// <summary>
        /// Get document with full details including category and uploader
        /// </summary>
        Task<Document?> GetDocumentWithDetailsAsync(int documentId);

        /// <summary>
        /// Check if document exists and user has access
        /// </summary>
        Task<bool> CanUserAccessDocumentAsync(int documentId, int userId, bool isAdmin = false);

        /// <summary>
        /// Update document download count
        /// </summary>
        Task UpdateDownloadCountAsync(int documentId);

        /// <summary>
        /// Get documents uploaded by user
        /// </summary>
        Task<IQueryable<Document>> GetDocumentsByUserAsync(int userId);

        /// <summary>
        /// Get document statistics
        /// </summary>
        Task<DocumentStatistics> GetDocumentStatisticsAsync();

        /// <summary>
        /// Soft delete document (mark as inactive)
        /// </summary>
        Task<bool> SoftDeleteDocumentAsync(int documentId);

        /// <summary>
        /// Restore soft deleted document
        /// </summary>
        Task<bool> RestoreDocumentAsync(int documentId);
    }

    /// <summary>
    /// Document statistics model
    /// </summary>
    public class DocumentStatistics
    {
        public int TotalDocuments { get; set; }
        public int ActiveDocuments { get; set; }
        public int InactiveDocuments { get; set; }
        public long TotalFileSize { get; set; }
        public int TotalDownloads { get; set; }
        public int DocumentsThisMonth { get; set; }
        public int DocumentsThisWeek { get; set; }
        public Dictionary<string, int> DocumentsByCategory { get; set; } = new();
        public Dictionary<string, int> DocumentsByExtension { get; set; } = new();
    }
}

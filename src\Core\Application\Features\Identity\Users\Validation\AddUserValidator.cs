using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using System.Text.RegularExpressions;
using Abstraction.Constants;
using Application.Features.Identity.Users.Commands.AddUser;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for AddUserCommand with Sprint 3 enhancements
    /// Implements mobile number validation and unique role checking
    /// </summary>
    public class AddUserValidator : AbstractValidator<AddUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        public AddUserValidator(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            Include(new BaseUserValidator(_localizer));
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {
            // Password Validation
            RuleFor(x => x.Password)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField])
                .MinimumLength(8)
                .WithMessage(_localizer[SharedResourcesKey.PasswordMinimumLength])
                .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
                .WithMessage(_localizer[SharedResourcesKey.PasswordComplexityError]);

            // Confirm Password Validation
            RuleFor(x => x.ConfirmPassword)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.ProfileRequiredField])
                .Equal(x => x.Password)
                .WithMessage(_localizer[SharedResourcesKey.PasswordMismatch]);

            // Role Validation
            RuleFor(x => x.Roles)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired])
                .Must(roles => roles.Count > 0)
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired]);

            // Role Selection Logic Validation
            RuleFor(x => x.Roles)
                .Must(BeValidRoleSelection)
                .WithMessage(_localizer[SharedResourcesKey.EditUserInvalidRoleSelection]);

            // File Validation
            RuleFor(x => x.CVFile)
                .Must(BeValidCVFile)
                .WithMessage(_localizer[SharedResourcesKey.EditUserInvalidCVFile])
                .When(x => !string.IsNullOrWhiteSpace(x.CVFile));

            RuleFor(x => x.PersonalPhoto)
                .Must(BeValidPhotoFile)
                .WithMessage(_localizer[SharedResourcesKey.ProfileInvalidPhotoFile])
                .When(x => !string.IsNullOrWhiteSpace(x.PersonalPhoto));
        }

        /// <summary>
        /// Validates role selection logic according to JDWA-1251 requirements
        /// Multi-select enabled ONLY IF roles are ('Fund Manager' AND 'Board Member') OR ('Associate Fund Manager' AND 'Board Member')
        /// Otherwise, single role selection only
        /// </summary>
        private bool BeValidRoleSelection(List<string> roles)
        {
            if (roles == null || roles.Count == 0)
                return false;

            // Single role is always valid
            if (roles.Count == 1)
                return true;

            // Multi-select is only allowed for specific combinations
            if (roles.Count == 2)
            {
                var hasValidCombination =
                    (roles.Contains(RoleHelper.FundManager) && roles.Contains(RoleHelper.BoardMember)) ||
                    (roles.Contains(RoleHelper.AssociateFundManager) && roles.Contains(RoleHelper.BoardMember));

                return hasValidCombination;
            }

            // More than 2 roles is not allowed
            return false;
        }

        /// <summary>
        /// Validates CV file format and size (string path validation)
        /// </summary>
        private bool BeValidCVFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return true; // Optional field

            // Basic validation - in real implementation, this would validate file path/URL
            // For now, just check if it's a reasonable string
            return filePath.Length <= 500; // Reasonable path length
        }

        /// <summary>
        /// Validates personal photo file format and size (string path validation)
        /// </summary>
        private bool BeValidPhotoFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return true; // Optional field

            // Basic validation - in real implementation, this would validate file path/URL
            // For now, just check if it's a reasonable string
            return filePath.Length <= 500; // Reasonable path length
        }
    }
}

using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using System.Text.RegularExpressions;
using Abstraction.Constants;
using Application.Features.Identity.Users.Commands.AddUser;

namespace Application.Features.Identity.Users.Validation
{
    /// <summary>
    /// Validator for AddUserCommand with Sprint 3 enhancements
    /// Implements mobile number validation and unique role checking
    /// </summary>
    public class AddUserValidator : AbstractValidator<AddUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        public AddUserValidator(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            Include(new BaseUserValidator(_localizer));
            ApplyValidationRules();
        }

        private void ApplyValidationRules()
        {
            // Role Validation
            RuleFor(x => x.Roles)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired])
                .Must(roles => roles.Count > 0)
                .WithMessage(_localizer[SharedResourcesKey.AtLeastOneRoleRequired]);



            
        }

       
 
    }
}

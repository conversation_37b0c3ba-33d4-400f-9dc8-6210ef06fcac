using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Enums;
using Application.Features.BoardMembers.Commands.Add;
using AutoMapper;
using Core.Abstraction.Contract.Service.Notifications;
using Domain.Entities.FundManagement;
using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Moq;
using Resources;
using System.Security.Claims;
using Xunit;

namespace Tests.Core.Application.UnitTests.Features.BoardMembers
{
    /// <summary>
    /// Unit tests for WhatsApp notification integration in AddBoardMemberCommandHandler
    /// Verifies that WhatsApp notifications are sent correctly when adding board members
    /// </summary>
    public class AddBoardMemberCommandHandlerWhatsAppTests
    {
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IIdentityServiceManager> _mockIdentityService;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly Mock<UserManager<User>> _mockUserManager;
        private readonly Mock<IWhatsAppNotificationService> _mockWhatsAppService;
        private readonly AddBoardMemberCommandHandler _handler;

        public AddBoardMemberCommandHandlerWhatsAppTests()
        {
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockLogger = new Mock<ILoggerManager>();
            _mockIdentityService = new Mock<IIdentityServiceManager>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();
            _mockUserManager = MockUserManager();
            _mockWhatsAppService = new Mock<IWhatsAppNotificationService>();

            _handler = new AddBoardMemberCommandHandler(
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockLogger.Object,
                _mockIdentityService.Object,
                _mockCurrentUserService.Object,
                _mockUserManager.Object,
                _mockWhatsAppService.Object);
        }

        [Fact]
        public async Task Handle_WithValidBoardMember_ShouldSendWhatsAppNotification()
        {
            // Arrange
            var command = new AddBoardMemberCommand
            {
                FundId = 1,
                UserId = 100,
                MemberType = BoardMemberType.Independent
            };

            var fund = CreateTestFund();
            var boardMember = CreateTestBoardMember(command);
            var user = CreateTestUser();

            SetupMocks(fund, boardMember, user);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _mockWhatsAppService.Verify(x => x.SendLocalizedMessageAsync(
                100,
                "+966501234567",
                WhatsAppMessageType.FundMemberAdded,
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithUserWithoutPhoneNumber_ShouldNotSendWhatsAppNotification()
        {
            // Arrange
            var command = new AddBoardMemberCommand
            {
                FundId = 1,
                UserId = 100,
                MemberType = BoardMemberType.Independent
            };

            var fund = CreateTestFund();
            var boardMember = CreateTestBoardMember(command);
            var user = CreateTestUserWithoutPhone();

            SetupMocks(fund, boardMember, user);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _mockWhatsAppService.Verify(x => x.SendLocalizedMessageAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<WhatsAppMessageType>(),
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        private void SetupMocks(Fund fund, BoardMember boardMember, User user)
        {
            _mockRepository.Setup(r => r.Funds.ViewFundUsers(It.IsAny<int>(), false))
                          .ReturnsAsync(fund);
            
            _mockMapper.Setup(m => m.Map<BoardMember>(It.IsAny<AddBoardMemberCommand>()))
                      .Returns(boardMember);
            
            _mockRepository.Setup(r => r.BoardMembers.AddAsync(It.IsAny<BoardMember>(), It.IsAny<CancellationToken>()))
                          .ReturnsAsync(boardMember);
            
            _mockUserManager.Setup(um => um.FindByIdAsync(It.IsAny<string>()))
                           .ReturnsAsync(user);
            
            _mockCurrentUserService.Setup(c => c.UserId).Returns(1);
            _mockCurrentUserService.Setup(c => c.UserName).Returns("TestUser");
            
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns(new LocalizedString("key", "value"));
            
            _mockWhatsAppService.Setup(w => w.SendLocalizedMessageAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<WhatsAppMessageType>(),
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new WhatsAppMessageResponseDto { IsSuccess = true, MessageId = "test-id" });
        }

        private Fund CreateTestFund()
        {
            return new Fund
            {
                Id = 1,
                Name = "Test Fund",
                FundManagers = new List<FundManager>(),
                FundBoardSecretaries = new List<FundBoardSecretary>(),
                BoardMembers = new List<BoardMember>()
            };
        }

        private BoardMember CreateTestBoardMember(AddBoardMemberCommand command)
        {
            return new BoardMember
            {
                Id = 1,
                FundId = command.FundId,
                UserId = command.UserId,
                MemberType = command.MemberType,
                IsActive = true
            };
        }

        private User CreateTestUser()
        {
            return new User
            {
                Id = 100,
                UserName = "testuser",
                PhoneNumber = "+966501234567",
                FullName = "Test User"
            };
        }

        private User CreateTestUserWithoutPhone()
        {
            return new User
            {
                Id = 100,
                UserName = "testuser",
                PhoneNumber = null,
                FullName = "Test User"
            };
        }

        private Mock<UserManager<User>> MockUserManager()
        {
            var store = new Mock<IUserStore<User>>();
            return new Mock<UserManager<User>>(store.Object, null, null, null, null, null, null, null, null);
        }
    }
}

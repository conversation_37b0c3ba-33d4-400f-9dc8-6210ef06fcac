using Application.Features.DocumentManagement.Commands.UploadDocument;
using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using System.Globalization;

namespace Application.Mapping.DocumentManagement
{
    /// <summary>
    /// AutoMapper profile for Document entity mappings
    /// </summary>
    public class DocumentMappingProfile : Profile
    {
        public DocumentMappingProfile()
        {
            CreateMap<Document, DocumentDto>()
                .ForMember(dest => dest.FormattedFileSize, opt => opt.MapFrom(src => FormatFileSize(src.Attachment.FileSize)))
                .ForMember(dest => dest.DownloadUrl, opt => opt.MapFrom(src => $"/api/documents/{src.Id}/download"))
                .ForMember(dest => dest.PreviewUrl, opt => opt.MapFrom(src => GetPreviewUrl(src)));

            CreateMap<DocumentCategory, DocumentCategoryDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => GetLocalizedCategoryName(src)));
        }

        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Get localized category name based on current culture
        /// </summary>
        private static string GetLocalizedCategoryName(DocumentCategory category)
        {
            if (category == null) return string.Empty;

            var culture = CultureInfo.CurrentCulture.Name;
            return culture.StartsWith("ar") ? category.NameAr : category.NameEn;
        }
 
        /// <summary>
        /// Get preview URL for supported file types
        /// </summary>
        private static string? GetPreviewUrl(Document document)
        {
            if (document?.Attachment == null) return null;

            var fileExtension = Path.GetExtension(document.Attachment.FileName).ToLowerInvariant();
            var supportedPreviewTypes = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            if (supportedPreviewTypes.Contains(fileExtension))
            {
                return $"/api/documents/{document.Id}/preview";
            }
            return null;
        }
    }
}

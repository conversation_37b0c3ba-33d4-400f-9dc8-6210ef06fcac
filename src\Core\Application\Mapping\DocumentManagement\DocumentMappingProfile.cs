using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Domain.Entities.DocumentManagement;
using System.Globalization;

namespace Application.Mapping.DocumentManagement
{
    /// <summary>
    /// AutoMapper profile for Document entity mappings
    /// </summary>
    public class DocumentMappingProfile : Profile
    {
        public DocumentMappingProfile()
        {
            CreateMap<Document, DocumentDto>()
                .ForMember(dest => dest.FileName, opt => opt.MapFrom(src => src.Attachment.FileName))
                .ForMember(dest => dest.FileSize, opt => opt.MapFrom(src => src.Attachment.FileSize))
                .ForMember(dest => dest.ContentType, opt => opt.MapFrom(src => src.Attachment.ContentType))
                .ForMember(dest => dest.FileExtension, opt => opt.MapFrom(src => Path.GetExtension(src.Attachment.FileName)))
                .ForMember(dest => dest.FileUrl, opt => opt.MapFrom(src => src.Attachment.Path))
                .ForMember(dest => dest.FormattedFileSize, opt => opt.MapFrom(src => FormatFileSize(src.Attachment.FileSize)))
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => GetLocalizedCategoryName(src.DocumentCategory)))
                .ForMember(dest => dest.UploadedByUserName, opt => opt.MapFrom(src => src.UploadedByUser != null ? src.UploadedByUser.FullName : "Unknown"))
                .ForMember(dest => dest.AccessLevel, opt => opt.MapFrom(src => src.AccessLevel.ToString()))
                .ForMember(dest => dest.DownloadUrl, opt => opt.MapFrom(src => $"/api/documents/{src.Id}/download"))
                .ForMember(dest => dest.PreviewUrl, opt => opt.MapFrom(src => GetPreviewUrl(src)))
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.CanDownload, opt => opt.Ignore()); // Set in handler

            CreateMap<DocumentCategory, DocumentCategoryDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => GetLocalizedCategoryName(src)))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => GetLocalizedCategoryDescription(src)))
                .ForMember(dest => dest.ParentCategoryName, opt => opt.MapFrom(src => src.ParentCategory != null ? GetLocalizedCategoryName(src.ParentCategory) : null))
                .ForMember(dest => dest.DocumentCount, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.FormattedMaxFileSize, opt => opt.MapFrom(src => src.MaxFileSize.HasValue ? FormatFileSize(src.MaxFileSize.Value) : null))
                .ForMember(dest => dest.ChildCategories, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.CanUpload, opt => opt.Ignore()); // Set in handler

            CreateMap<UploadDocumentDto, Document>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.AttachmentId, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.UploadedByUserId, opt => opt.Ignore()) // Set in handler
                .ForMember(dest => dest.Version, opt => opt.MapFrom(src => 1))
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.DownloadCount, opt => opt.MapFrom(src => 0))
                .ForMember(dest => dest.LastAccessedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DocumentCategory, opt => opt.Ignore())
                .ForMember(dest => dest.UploadedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore());
        }

        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Get localized category name based on current culture
        /// </summary>
        private static string GetLocalizedCategoryName(DocumentCategory category)
        {
            if (category == null) return string.Empty;

            var culture = CultureInfo.CurrentCulture.Name;
            return culture.StartsWith("ar") ? category.NameAr : category.NameEn;
        }

        /// <summary>
        /// Get localized category description based on current culture
        /// </summary>
        private static string? GetLocalizedCategoryDescription(DocumentCategory category)
        {
            if (category == null) return null;

            var culture = CultureInfo.CurrentCulture.Name;
            return culture.StartsWith("ar") ? category.DescriptionAr : category.DescriptionEn;
        }

        /// <summary>
        /// Get preview URL for supported file types
        /// </summary>
        private static string? GetPreviewUrl(Document document)
        {
            if (document?.Attachment == null) return null;

            var fileExtension = Path.GetExtension(document.Attachment.FileName).ToLowerInvariant();
            var supportedPreviewTypes = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            if (supportedPreviewTypes.Contains(fileExtension))
            {
                return $"/api/documents/{document.Id}/preview";
            }
            return null;
        }
    }
}

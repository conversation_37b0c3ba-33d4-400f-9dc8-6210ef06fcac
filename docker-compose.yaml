services:
  redis:
    image: redis:latest
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    networks:
      - jadwa-network
  jadwa-api:
    build: ./
    container_name: jadwa-api
    restart: always
    depends_on:
      - redis
    networks:
      - jadwa-network
    environment:
      - ConnectionStrings__Redis=redis:6379
      - ASPNETCORE_Kestrel__Endpoints__Https__Url=https://+:8081
      - ASPNETCORE_Kestrel__Endpoints__Http__Url=http://+:8080
      - ASPNETCORE_Kestrel__Endpoints__Https__Certificate__Path=/https/aspnetapp.pfx
      - ASPNETCORE_Kestrel__Endpoints__Https__Certificate__Password=123456
    volumes:
      - ./aspnetapp.pfx:/https/aspnetapp.pfx:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro

networks:
  jadwa-network:
    driver: bridge
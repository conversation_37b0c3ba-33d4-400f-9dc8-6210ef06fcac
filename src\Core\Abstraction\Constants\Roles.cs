﻿using System.ComponentModel;
using System.Reflection;

namespace Abstraction.Constants
{
    public enum Roles
    {
        [Description("None")]
        None,

        [Description("مشرف عام")]
        SuperAdmin,

        [Description("مشرف")]
        Admin,

        [Description("مستخدم أساسي")]
        Basic,

        [Description("مستخدم")]
        User,

        [Description("مدير صندوق")]
        FundManager,

        [Description("المستشار القانوني")]
        LegalCouncil,

        [Description("سكرتير المجلس")]
        BoardSecretary,

        [Description("عضو مجلس الإدارة")]
        BoardMember,

        [Description("مدير المالية")]
        FinanceController,

        [Description("مدير الادارة")]
        ComplianceLegalManagingDirector,

        [Description("مدير العقارات")]
        HeadOfRealEstate
    }

    /// <summary>
    /// Helper class for role validation and comparison
    /// Handles the fact that roles are stored in lowercase in the database
    /// but need to be compared against enum values
    /// </summary>
    public static class RoleHelper
    {
        // Role names as stored in database (lowercase)
        public const string FUND_MANAGER = "fundmanager";
        public const string LEGAL_COUNCIL = "legalcouncil";
        public const string BOARD_SECRETARY = "boardsecretary";
        public const string BOARD_MEMBER = "boardmember";
        public const string SUPER_ADMIN = "superadmin";
        public const string ADMIN = "admin";
        public const string BASIC = "basic";
        public const string USER = "user";

        public const string FinanceController = "FinanceController";
        public const string ComplianceLegalManagingDirector = "ComplianceLegalManagingDirector";
        public const string HeadOfRealEstate = "HeadOfRealEstate";

        /// <summary>
        /// Checks if user has Fund Manager role
        /// </summary>
        public static bool IsFundManager(IList<string> userRoles)
        {
            return userRoles.Any(role => string.Equals(role, FUND_MANAGER, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if user has Legal Council role
        /// </summary>
        public static bool IsLegalCouncil(IList<string> userRoles)
        {
            return userRoles.Any(role => string.Equals(role, LEGAL_COUNCIL, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if user has Board Secretary role
        /// </summary>
        public static bool IsBoardSecretary(IList<string> userRoles)
        {
            return userRoles.Any(role => string.Equals(role, BOARD_SECRETARY, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if user has Board Member role
        /// </summary>
        public static bool IsBoardMember(IList<string> userRoles)
        {
            return userRoles.Any(role => string.Equals(role, BOARD_MEMBER, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if user has any authorized role for resolution operations
        /// </summary>
        public static bool HasAuthorizedRole(IList<string> userRoles)
        {
            return IsFundManager(userRoles) || IsLegalCouncil(userRoles) || IsBoardSecretary(userRoles);
        }
    }
}
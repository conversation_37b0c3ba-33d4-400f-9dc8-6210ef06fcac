﻿using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Application.Features.Funds.Dtos;
using Application.Features.Notifications.Dtos;
using Application.Features.Notifications.Queries.List;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Presentation.Controllers.Notification
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NotificationController : AppControllerBase
    {
        [HttpGet("NotitficationList")]
        [ProducesResponseType(typeof(BaseResponse<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetList()
        {
            var response = await Mediator.Send(new ListQuery { });
            return NewResult(response);
        }

        //[HttpGet("UnReadedNotificationList")]
        //[ProducesResponseType(typeof(PaginatedResult<NotificationDto>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> GetUnreadedList([FromQuery] ListUnreadedQuery query)
        //{
        //    var response = await Mediator.Send(query);
        //    return NewResult(response);
        //}


    }
}

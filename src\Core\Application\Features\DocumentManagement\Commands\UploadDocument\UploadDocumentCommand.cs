using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Application.Features.DocumentManagement.Commands.UploadDocument
{
    /// <summary>
    /// Command for uploading a document
    /// </summary>
    public class UploadDocumentCommand : ICommand<BaseResponse<DocumentUploadResponseDto>>
    {
        /// <summary>
        /// Document name/title
        /// </summary>
        [Required(ErrorMessage = "Document name is required")]
        [StringLength(255, ErrorMessage = "Document name cannot exceed 255 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Document category ID
        /// </summary>
        [Required(ErrorMessage = "Document category is required")]
        public int DocumentCategoryId { get; set; }

        /// <summary>
        /// Document description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Document tags (comma-separated)
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// Access level (1=Public, 2=Private, 3=Restricted)
        /// </summary>
        public int AccessLevel { get; set; } = 2; // Default to Private

        /// <summary>
        /// File to upload
        /// </summary>
        [Required(ErrorMessage = "File is required")]
        public IFormFile File { get; set; } = null!;
    }
}

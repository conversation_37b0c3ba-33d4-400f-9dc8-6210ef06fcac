using Application.Features.DocumentManagement.Commands.DeleteDocument;
using FluentValidation;

namespace Application.Features.DocumentManagement.Validation
{
    /// <summary>
    /// Validator for DeleteDocumentCommand
    /// </summary>
    public class DeleteDocumentCommandValidator : AbstractValidator<DeleteDocumentCommand>
    {
        public DeleteDocumentCommandValidator()
        {
            RuleFor(x => x.DocumentId)
                .GreaterThan(0).WithMessage("Valid document ID is required");
        }
    }
}

﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using DocumentFormat.OpenXml.Spreadsheet;



namespace Application.Features.Identity.Users.Commands.EditUser
{
    /// <summary>
    /// Enhanced handler for editing users with Sprint 3 administrative features
    /// Includes role management and advanced user properties
    /// </summary>
    public class EditUserCommandHandler : BaseResponseHandler, ICommandHandler<EditUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        // TODO: Add INotificationService when available
        // TODO: Add IFileUploadService when available
        #endregion

        #region Constructors
        public EditUserCommandHandler(
            IIdentityServiceManager identityServiceManager,
            I<PERSON>apper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _mapper = mapper;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(EditUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get existing user
                var existingUser = await _identityServiceManager.UserManagmentService.FindByIdAsync(request.Id.ToString());
                if (existingUser == null)
                    return NotFound<string>(_localizer[SharedResourcesKey.UserNotFound]);

                // Check email uniqueness
                var userWithEmail = await _identityServiceManager.UserManagmentService.FindByEmailAsync(request.Email);
                if (userWithEmail != null && userWithEmail.Id != request.Id)
                    return BadRequest<string>(_localizer[SharedResourcesKey.ProfileDuplicateEmail]);

                // Check username uniqueness
                var userWithUsername = await _identityServiceManager.UserManagmentService.FindByNameAsync(request.UserName);
                if (userWithUsername != null && userWithUsername.Id != request.Id)
                    return BadRequest<string>("Username is already in use");

                // Handle file uploads
                if (request.CVFile != null)
                {
                    // TODO: Implement file upload service integration
                    existingUser.CVFilePath = request.CVFile;
                }

                if (request.PersonalPhoto != null)
                {
                    // TODO: Implement file upload service integration
                    existingUser.PersonalPhotoPath =request.PersonalPhoto;
                }

                // Map basic fields using AutoMapper
                _mapper.Map(request, existingUser);

               

                // Update audit fields
                existingUser.UpdatedAt = DateTime.UtcNow;
                existingUser.UpdatedBy = _currentUserService.UserId;
                existingUser.UpdatedBy =2;
                // Save user changes
                var result = await _identityServiceManager.UserManagmentService.UpdateAsync(existingUser);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.SystemErrorSavingData]}: {errors}");
                }

                // Handle role updates
                var updatedRoles = new List<string>();
                await _identityServiceManager.AuthorizationService.EditUserRoles(request.Roles, existingUser);

                if (request.Roles.Contains(RoleHelper.HeadOfRealEstate) || request.Roles.Contains(RoleHelper.FinanceController) || request.Roles.Contains(RoleHelper.ComplianceLegalManagingDirector))
                {
                    foreach (var role in request.Roles)
                    {
                        var updatedUser = await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(role, existingUser.Id);
                        updatedUser.IsActive = false;
                        await _identityServiceManager.UserManagmentService.UpdateAsync(updatedUser);
                    }
                }
                return Success<string>(_localizer[SharedResourcesKey.UserUpdatedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion
    }
}

﻿

using Abstraction.Contracts.Repository;

namespace Abstraction.Contract.Repository.Fund
{
    public interface IFundRepository :IGenericRepository
    {
        Task<Domain.Entities.FundManagement.Fund> ViewFundDetails(int id, bool trackChanges);
        Task<Domain.Entities.FundManagement.Fund> ViewFundUsers(int id, bool trackChanges);
        Task<Domain.Entities.FundManagement.Fund> EditFundById(int id, bool trackChanges);
        IQueryable<Domain.Entities.FundManagement.Fund> GetAllAndInclude(bool trackChanges, int userId);
    }
}

﻿using AutoMapper;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using Abstraction.Contracts.Logger;
using Core.Abstraction.Contract.Service.Notifications;
using System.Linq;


namespace Application.Features.Identity.Users.Commands.AddUser
{
    /// <summary>
    /// Enhanced handler for adding users with Sprint 3 administrative features
    /// Includes role assignment and registration message integration
    /// </summary>
    public class AddUserCommandHandler : BaseResponseHandler, ICommandHandler<AddUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        private readonly IWhatsAppNotificationService _whatsAppService;
        private readonly ILoggerManager _logger;
        // TODO: Add IFileUploadService when available
        #endregion

        #region Constructors
        public AddUserCommandHandler(
            IIdentityServiceManager identityServiceManager,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService,
            IWhatsAppNotificationService whatsAppService,
            ILoggerManager logger)
        {
            _mapper = mapper;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
            _currentUserService = currentUserService;
            _whatsAppService = whatsAppService;
            _logger = logger;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(AddUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if user exists by email
                var existingUserByEmail = await _identityServiceManager.UserManagmentService.FindByEmailAsync(request.Email);
                if (existingUserByEmail != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.ProfileDuplicateEmail]);

                // Check if user exists by username
                var existingUserByUserName = await _identityServiceManager.UserManagmentService.FindByNameAsync(request.UserName);
                if (existingUserByUserName != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.UsernameAlreadyInUse]);

                // Handle file uploads
                string? cvFilePath = null;
                string? photoFilePath = null;

                if (request.CVFile != null)
                {
                    // TODO: Implement file upload service integration
                    cvFilePath = request.CVFile;
                }

                if (request.PersonalPhoto != null)
                {
                    // TODO: Implement file upload service integration
                    photoFilePath = request.PersonalPhoto;
                }

                // Map to user entity
                var user = _mapper.Map<User>(request);

                // Set Sprint 3 specific fields
                user.CVFilePath = cvFilePath;
                user.CountryCode = "+966";
                user.PreferredLanguage = "ar-EG"; // Default to Arabic
                user.PhoneNumber = request.UserName;
                user.PersonalPhotoPath = photoFilePath;
                user.RegistrationMessageIsSent = request.Roles.All(c => c == "Board Member") ? false : true ;
                user.RegistrationIsCompleted = false;
                user.CreatedAt = DateTime.UtcNow;
                user.CreatedBy = _currentUserService.UserId;
                user.CreatedBy = 2;
                // Create user
                var result = await _identityServiceManager.UserManagmentService.CreateAsync(user, request.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.SystemErrorSavingData]}: {errors}");
                }

                // Assign roles
                var assignedRoles = new List<string>();
                if (request.Roles.Any())
                {
                    foreach (var role in request.Roles)
                    {
                        var roleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist(role);
                        if (roleExists)
                        {
                            await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, role);
                            assignedRoles.Add(role);
                        }
                    }
                }
                else
                {
                    // Default role assignment
                    var defaultRoleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist("USER");
                    if (!defaultRoleExists)
                    {
                        await _identityServiceManager.AuthorizationService.AddRoleAsync("USER", null);
                    }
                    await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, "USER");
                    assignedRoles.Add("USER");
                }
                if(request.Roles.Contains(RoleHelper.HeadOfRealEstate) || request.Roles.Contains(RoleHelper.FinanceController) || request.Roles.Contains(RoleHelper.ComplianceLegalManagingDirector) )
                {
                    foreach (var role in request.Roles)
                    {
                        var updatedUser =await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(role, user.Id);
                        updatedUser.IsActive =false;
                        await _identityServiceManager.UserManagmentService.UpdateAsync(updatedUser);
                    }
                }

                // Prepare response


                // Send registration message if eligible (not only Board Member)
                if (user.RegistrationMessageIsSent)
                {
                    await SendWhatsAppRegistrationNotificationAsync(user, assignedRoles, request.Password);
                }

                return Success<string>(_localizer[SharedResourcesKey.UserAddedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }

        /// <summary>
        /// Sends WhatsApp registration notification to newly created user
        /// Implements MSG-ADD-008 from Sprint 3 requirements
        /// Only sends if user is eligible (not only Board Member role)
        /// </summary>
        private async Task SendWhatsAppRegistrationNotificationAsync(User user, List<string> userRoles, string temporaryPassword)
        {
            try
            {
                _logger.LogInfo($"Sending WhatsApp registration notification to user {user.Id}");

                // Format phone number for WhatsApp
                var phoneNumber = FormatInternationalPhoneNumber(user.PhoneNumber);
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    _logger.LogWarn($"Invalid phone number for user {user.Id}. WhatsApp notification skipped.");
                    return;
                }

                // Get primary role for message
                var primaryRole = userRoles.FirstOrDefault() ?? "User";
                var loginUrl = "https://jadwa-fund-management.com/login"; // TODO: Get from configuration

                // Send WhatsApp registration notification
                var response = await _whatsAppService.SendUserRegistrationMessageAsync(
                    user.Id,
                    phoneNumber,
                    user.UserName,
                    loginUrl,
                    CancellationToken.None);

                if (response.IsSuccess)
                {
                    _logger.LogInfo($"WhatsApp registration notification sent successfully to user {user.Id}. MessageId: {response.MessageId}");
                }
                else
                {
                    _logger.LogWarn($"WhatsApp registration notification failed for user {user.Id}. Error: {response.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                // Log WhatsApp failure but don't fail the entire operation (MSG-ADD-009)
                _logger.LogError(ex, $"Failed to send WhatsApp registration notification to user {user.Id}");
            }
        }

        /// <summary>
        /// Formats phone number for international WhatsApp delivery
        /// Handles Egyptian and Saudi number formats
        /// </summary>
        private string? FormatInternationalPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return null;

            // Remove all non-digit characters
            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());

            if (digits.Length < 10 || digits.Length > 15)
                return null;

            // Handle Egypt formats (+20)
            if (digits.StartsWith("020") && digits.Length >= 12 && digits.Length <= 14)
            {
                // Remove the extra 0 after country code (020 -> 20)
                return $"+{digits.Substring(1)}";
            }
            else if (digits.StartsWith("01") && digits.Length == 11)
            {
                // Egyptian mobile number starting with 01 (remove leading 0, add +20)
                return $"+20{digits.Substring(1)}";
            }
            else if (digits.Length == 10 && digits.StartsWith("1"))
            {
                // Egyptian mobile without leading 0
                return $"+20{digits}";
            }
            // Handle Saudi formats (+966)
            else if (digits.StartsWith("966") && digits.Length >= 12 && digits.Length <= 13)
            {
                // Already has country code
                return $"+{digits}";
            }
            else if (digits.StartsWith("05") && digits.Length == 10)
            {
                // Saudi mobile number starting with 05 (remove leading 0, add +966)
                return $"+966{digits.Substring(1)}";
            }
            else if (digits.Length == 9 && digits.StartsWith("5"))
            {
                // Saudi mobile without leading 0
                return $"+966{digits}";
            }
            // Handle other international formats
            else if (phoneNumber.StartsWith("+"))
            {
                return phoneNumber;
            }

            return null;
        }
        #endregion
    }
}

﻿using AutoMapper;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;


namespace Application.Features.Identity.Users.Commands.AddUser
{
    /// <summary>
    /// Enhanced handler for adding users with Sprint 3 administrative features
    /// Includes role assignment and registration message integration
    /// </summary>
    public class AddUserCommandHandler : BaseResponseHandler, ICommandHandler<AddUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        // TODO: Add INotificationService when available
        // TODO: Add IFileUploadService when available
        #endregion

        #region Constructors
        public AddUserCommandHandler(
            IIdentityServiceManager identityServiceManager,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _mapper = mapper;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(AddUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if user exists by email
                var existingUserByEmail = await _identityServiceManager.UserManagmentService.FindByEmailAsync(request.Email);
                if (existingUserByEmail != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.ProfileDuplicateEmail]);

                // Check if user exists by username
                var existingUserByUserName = await _identityServiceManager.UserManagmentService.FindByNameAsync(request.UserName);
                if (existingUserByUserName != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.UsernameAlreadyInUse]);

                // Handle file uploads
                string? cvFilePath = null;
                string? photoFilePath = null;

                if (request.CVFile != null)
                {
                    // TODO: Implement file upload service integration
                    cvFilePath = request.CVFile;
                }

                if (request.PersonalPhoto != null)
                {
                    // TODO: Implement file upload service integration
                    photoFilePath = request.PersonalPhoto;
                }

                // Map to user entity
                var user = _mapper.Map<User>(request);

                // Set Sprint 3 specific fields
                user.CVFilePath = cvFilePath;
                user.CountryCode = "+966";
                user.PreferredLanguage = "ar-EG"; // Default to Arabic
                user.PhoneNumber = request.UserName;
                user.PersonalPhotoPath = photoFilePath;
                user.RegistrationMessageIsSent = request.Roles.All(c => c == "Board Member") ? false : true ;
                user.RegistrationIsCompleted = false;
                user.CreatedAt = DateTime.UtcNow;
                user.CreatedBy = _currentUserService.UserId;
                user.CreatedBy = 2;
                // Create user
                var result = await _identityServiceManager.UserManagmentService.CreateAsync(user, request.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.SystemErrorSavingData]}: {errors}");
                }

                // Assign roles
                var assignedRoles = new List<string>();
                if (request.Roles.Any())
                {
                    foreach (var role in request.Roles)
                    {
                        var roleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist(role);
                        if (roleExists)
                        {
                            await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, role);
                            assignedRoles.Add(role);
                        }
                    }
                }
                else
                {
                    // Default role assignment
                    var defaultRoleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist("USER");
                    if (!defaultRoleExists)
                    {
                        await _identityServiceManager.AuthorizationService.AddRoleAsync("USER", null);
                    }
                    await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, "USER");
                    assignedRoles.Add("USER");
                }
                if(request.Roles.Contains(RoleHelper.HeadOfRealEstate) || request.Roles.Contains(RoleHelper.FinanceController) || request.Roles.Contains(RoleHelper.ComplianceLegalManagingDirector) )
                {
                    foreach (var role in request.Roles)
                    {
                        var updatedUser =await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(role, user.Id);
                        updatedUser.IsActive =false;
                        await _identityServiceManager.UserManagmentService.UpdateAsync(updatedUser);
                    }
                }

                // Prepare response


                // Send registration message if Not boardmmember  
                if (user.RegistrationMessageIsSent)
                {
                    user.RegistrationMessageIsSent = true;
                   // await _identityServiceManager.UserManagmentService.UpdateAsync(user);

                    // TODO: Send registration message when notification service is available
                    // await _notificationService.SendRegistrationMessageAsync(user.Id, request.WelcomeMessage);
                }

                return Success<string>(_localizer[SharedResourcesKey.UserAddedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion
    }
}

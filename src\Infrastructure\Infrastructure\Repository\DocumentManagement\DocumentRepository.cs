using Abstraction.Contract.Repository.DocumentManagement;
using Abstraction.Contract.Service;
using Domain.Entities.DocumentManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.DocumentManagement
{
    /// <summary>
    /// Repository implementation for Document entity operations
    /// </summary>
    public class DocumentRepository : GenericRepository, IDocumentRepository
    {
        #region Constructor
        public DocumentRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get documents with filters and includes
        /// </summary>
        public async Task<IQueryable<Document>> GetDocumentsWithFiltersAsync(
            int? categoryId = null,
            string? searchTerm = null,
            string? fileExtension = null,
            int? accessLevel = null,
            int? uploadedByUserId = null,
            bool? isActive = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? tags = null,
            int currentUserId = 0,
            bool isAdmin = false)
        {
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Include(d => d.Attachment)
                .AsQueryable();

            // Apply filters
            if (categoryId.HasValue)
                query = query.Where(d => d.DocumentCategoryId == categoryId.Value);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(d => 
                    d.Name.ToLower().Contains(lowerSearchTerm) ||
                    d.FileName.ToLower().Contains(lowerSearchTerm) ||
                    (d.Description != null && d.Description.ToLower().Contains(lowerSearchTerm)) ||
                    (d.Tags != null && d.Tags.ToLower().Contains(lowerSearchTerm)));
            }

            if (!string.IsNullOrEmpty(fileExtension))
                query = query.Where(d => d.FileExtension.ToLower() == fileExtension.ToLower());

            if (accessLevel.HasValue)
                query = query.Where(d => (int)d.AccessLevel == accessLevel.Value);

            if (uploadedByUserId.HasValue)
                query = query.Where(d => d.UploadedByUserId == uploadedByUserId.Value);

            if (isActive.HasValue)
                query = query.Where(d => d.IsActive == isActive.Value);

            if (fromDate.HasValue)
                query = query.Where(d => d.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(d => d.CreatedAt <= toDate.Value);

            if (!string.IsNullOrEmpty(tags))
            {
                var tagList = tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(t => t.Trim().ToLower()).ToList();
                query = query.Where(d => d.Tags != null && 
                    tagList.Any(tag => d.Tags.ToLower().Contains(tag)));
            }

            // Apply access control
            if (!isAdmin)
            {
                query = query.Where(d => 
                    d.AccessLevel == DocumentAccessLevel.Public ||
                    d.UploadedByUserId == currentUserId);
            }

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Get document count by category
        /// </summary>
        public async Task<int> GetDocumentCountByCategoryAsync(int categoryId)
        {
            return await RepositoryContext.Set<Document>()
                .Where(d => d.DocumentCategoryId == categoryId && d.IsActive)
                .CountAsync();
        }

        /// <summary>
        /// Get documents by category with pagination
        /// </summary>
        public async Task<IQueryable<Document>> GetDocumentsByCategoryAsync(int categoryId, bool includeInactive = false)
        {
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Where(d => d.DocumentCategoryId == categoryId);

            if (!includeInactive)
                query = query.Where(d => d.IsActive);

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Get recent documents for user
        /// </summary>
        public async Task<List<Document>> GetRecentDocumentsAsync(int userId, int count = 10)
        {
            return await RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Where(d => d.IsActive && (
                    d.AccessLevel == DocumentAccessLevel.Public ||
                    d.UploadedByUserId == userId))
                .OrderByDescending(d => d.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// Get popular documents (by download count)
        /// </summary>
        public async Task<List<Document>> GetPopularDocumentsAsync(int count = 10)
        {
            return await RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Where(d => d.IsActive && d.AccessLevel == DocumentAccessLevel.Public)
                .OrderByDescending(d => d.DownloadCount)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// Search documents by text
        /// </summary>
        public async Task<IQueryable<Document>> SearchDocumentsAsync(string searchTerm, int? categoryId = null)
        {
            var lowerSearchTerm = searchTerm.ToLower();
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Where(d => d.IsActive && (
                    d.Name.ToLower().Contains(lowerSearchTerm) ||
                    d.FileName.ToLower().Contains(lowerSearchTerm) ||
                    (d.Description != null && d.Description.ToLower().Contains(lowerSearchTerm)) ||
                    (d.Tags != null && d.Tags.ToLower().Contains(lowerSearchTerm))));

            if (categoryId.HasValue)
                query = query.Where(d => d.DocumentCategoryId == categoryId.Value);

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Get documents by tags
        /// </summary>
        public async Task<IQueryable<Document>> GetDocumentsByTagsAsync(string[] tags)
        {
            var lowerTags = tags.Select(t => t.ToLower()).ToList();
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .Where(d => d.IsActive && d.Tags != null &&
                    lowerTags.Any(tag => d.Tags.ToLower().Contains(tag)));

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Get document with full details including category and uploader
        /// </summary>
        public async Task<Document?> GetDocumentWithDetailsAsync(int documentId)
        {
            return await RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Include(d => d.UploadedByUser)
                .FirstOrDefaultAsync(d => d.Id == documentId);
        }

        /// <summary>
        /// Check if document exists and user has access
        /// </summary>
        public async Task<bool> CanUserAccessDocumentAsync(int documentId, int userId, bool isAdmin = false)
        {
            var document = await RepositoryContext.Set<Document>()
                .FirstOrDefaultAsync(d => d.Id == documentId && d.IsActive);

            if (document == null) return false;

            if (isAdmin) return true;

            return document.AccessLevel switch
            {
                DocumentAccessLevel.Public => true,
                DocumentAccessLevel.Private => document.UploadedByUserId == userId,
                DocumentAccessLevel.Restricted => document.UploadedByUserId == userId,
                _ => false
            };
        }

        /// <summary>
        /// Update document download count
        /// </summary>
        public async Task UpdateDownloadCountAsync(int documentId)
        {
            var document = await RepositoryContext.Set<Document>()
                .FirstOrDefaultAsync(d => d.Id == documentId);

            if (document != null)
            {
                document.DownloadCount++;
                document.LastAccessedAt = DateTime.UtcNow;
                await RepositoryContext.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Get documents uploaded by user
        /// </summary>
        public async Task<IQueryable<Document>> GetDocumentsByUserAsync(int userId)
        {
            var query = RepositoryContext.Set<Document>()
                .Include(d => d.DocumentCategory)
                .Where(d => d.UploadedByUserId == userId);

            return await Task.FromResult(query);
        }

        /// <summary>
        /// Get document statistics
        /// </summary>
        public async Task<DocumentStatistics> GetDocumentStatisticsAsync()
        {
            var documents = RepositoryContext.Set<Document>();
            var categories = RepositoryContext.Set<DocumentCategory>();

            var stats = new DocumentStatistics
            {
                TotalDocuments = await documents.CountAsync(),
                ActiveDocuments = await documents.CountAsync(d => d.IsActive),
                InactiveDocuments = await documents.CountAsync(d => !d.IsActive),
                TotalFileSize = await documents.SumAsync(d => d.FileSize),
                TotalDownloads = await documents.SumAsync(d => d.DownloadCount),
                DocumentsThisMonth = await documents.CountAsync(d => d.CreatedAt >= DateTime.UtcNow.AddMonths(-1)),
                DocumentsThisWeek = await documents.CountAsync(d => d.CreatedAt >= DateTime.UtcNow.AddDays(-7))
            };

            // Documents by category
            var categoryStats = await documents
                .Include(d => d.DocumentCategory)
                .GroupBy(d => d.DocumentCategory.NameEn)
                .Select(g => new { Category = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Category, x => x.Count);
            stats.DocumentsByCategory = categoryStats;

            // Documents by extension
            var extensionStats = await documents
                .GroupBy(d => d.FileExtension)
                .Select(g => new { Extension = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Extension, x => x.Count);
            stats.DocumentsByExtension = extensionStats;

            return stats;
        }

        /// <summary>
        /// Soft delete document (mark as inactive)
        /// </summary>
        public async Task<bool> SoftDeleteDocumentAsync(int documentId)
        {
            var document = await RepositoryContext.Set<Document>()
                .FirstOrDefaultAsync(d => d.Id == documentId);

            if (document != null)
            {
                document.IsActive = false;
                await RepositoryContext.SaveChangesAsync();
                return true;
            }

            return false;
        }

        /// <summary>
        /// Restore soft deleted document
        /// </summary>
        public async Task<bool> RestoreDocumentAsync(int documentId)
        {
            var document = await RepositoryContext.Set<Document>()
                .FirstOrDefaultAsync(d => d.Id == documentId);

            if (document != null)
            {
                document.IsActive = true;
                await RepositoryContext.SaveChangesAsync();
                return true;
            }

            return false;
        }

        #endregion
    }
}

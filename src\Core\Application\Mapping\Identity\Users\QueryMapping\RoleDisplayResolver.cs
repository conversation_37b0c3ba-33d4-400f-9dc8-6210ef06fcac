using AutoMapper;
using Abstraction.Contracts.Identity;
using Domain.Entities.Users;
using Abstraction.Contracts.Logger;
using Application.Features.Identity.Users.Queries.Responses;

namespace Application.Mapping.Users
{
    /// <summary>
    /// Custom AutoMapper value resolver for user primary role display
    /// Provides the primary role name for a user with proper filtering and error handling
    /// Note: This resolver is currently not used in favor of the EnhanceWithRoleInformation approach
    /// which provides better performance and error handling
    /// </summary>
    public class RoleDisplayResolver(IIdentityServiceManager identityServiceManager) : IValueResolver<User, GetUserListResponse, string>
    {
        private readonly IIdentityServiceManager _identityServiceManager = identityServiceManager;

        public string Resolve(User source, GetUserListResponse destination, string destMember, ResolutionContext context)
        {
            try
            {
                // Note: Using async methods in AutoMapper resolvers is not recommended
                // This is why the EnhanceWithRoleInformation approach is preferred
                var user = _identityServiceManager.UserManagmentService.FindByIdAsync(source.Id.ToString()).GetAwaiter().GetResult();
                if (user != null)
                {
                    var roles = _identityServiceManager.AuthorizationService.GetUsersRoles(user).GetAwaiter().GetResult();
                    if (roles?.UserRoles != null)
                    {
                        // Filter to only roles where HasRole = true
                        return roles.UserRoles
                            .Where(r => r.HasRole)
                            .Select(r => r.Name)
                            .FirstOrDefault() ?? string.Empty;
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Custom AutoMapper value resolver for user roles list display
    /// Provides all role names for a user with proper filtering and error handling
    /// Note: This resolver is currently not used in favor of the EnhanceWithRoleInformation approach
    /// which provides better performance and error handling
    /// </summary>
    public class RolesDisplayResolver(IIdentityServiceManager identityServiceManager) : IValueResolver<User, GetUserListResponse, List<string>>
    {
        private readonly IIdentityServiceManager _identityServiceManager = identityServiceManager;

        public List<string> Resolve(User source, GetUserListResponse destination, List<string> destMember, ResolutionContext context)
        {
            try
            {
                // Note: Using async methods in AutoMapper resolvers is not recommended
                // This is why the EnhanceWithRoleInformation approach is preferred
                var user = _identityServiceManager.UserManagmentService.FindByIdAsync(source.Id.ToString()).GetAwaiter().GetResult();
                if (user != null)
                {
                    var roles = _identityServiceManager.AuthorizationService.GetUsersRoles(user).GetAwaiter().GetResult();
                    if (roles?.UserRoles != null)
                    {
                        // Filter to only roles where HasRole = true
                        return roles.UserRoles
                            .Where(r => r.HasRole)
                            .Select(r => r.Name)
                            .ToList();
                    }
                }
                return new List<string>();
            }
            catch (Exception ex)
            {
                return new List<string>();
            }
        }
    }
}

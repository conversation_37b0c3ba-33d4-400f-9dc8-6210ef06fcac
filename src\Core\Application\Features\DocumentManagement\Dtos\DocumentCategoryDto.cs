using Abstraction.Base.Dto;

namespace Application.Features.DocumentManagement.Dtos
{
    /// <summary>
    /// Document category data transfer object
    /// </summary>
    public class DocumentCategoryDto : BaseDto
    {
        /// <summary>
        /// Category name in Arabic
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Category name in English
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        /// <summary>
        /// Localized category name based on current culture
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Category description in Arabic
        /// </summary>
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Category description in English
        /// </summary>
        public string? DescriptionEn { get; set; }

        /// <summary>
        /// Localized description based on current culture
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Category icon/symbol
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether category is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Parent category ID
        /// </summary>
        public int? ParentCategoryId { get; set; }

        /// <summary>
        /// Parent category name
        /// </summary>
        public string? ParentCategoryName { get; set; }

        /// <summary>
        /// Number of documents in this category
        /// </summary>
        public int DocumentCount { get; set; }

        /// <summary>
        /// Maximum file size allowed (in bytes)
        /// </summary>
        public long? MaxFileSize { get; set; }

        /// <summary>
        /// Formatted maximum file size
        /// </summary>
        public string? FormattedMaxFileSize { get; set; }

        /// <summary>
        /// Allowed file extensions
        /// </summary>
        public string? AllowedExtensions { get; set; }

        /// <summary>
        /// Category color for UI
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// Child categories
        /// </summary>
        public List<DocumentCategoryDto> ChildCategories { get; set; } = new();

        /// <summary>
        /// Whether current user can upload to this category
        /// </summary>
        public bool CanUpload { get; set; }
    }
}

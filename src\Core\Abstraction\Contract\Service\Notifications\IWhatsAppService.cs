namespace Abstraction.Contract.Service.Notifications
{
    /// <summary>
    /// Service interface for WhatsApp messaging functionality
    /// Supports user registration messages and password reset notifications
    /// </summary>
    public interface IWhatsAppService
    {
        /// <summary>
        /// Sends a registration message to a user via WhatsApp
        /// Used when a Board Member gets Fund Manager/Associate Fund Manager role
        /// </summary>
        /// <param name="phoneNumber">User's phone number in international format</param>
        /// <param name="userName">User's full name</param>
        /// <param name="roleName">The new role assigned to the user</param>
        /// <param name="loginUrl">URL for the user to access the system</param>
        /// <returns>True if message was sent successfully</returns>
        Task<bool> SendRegistrationMessageAsync(string phoneNumber, string userName, string roleName, string loginUrl);

        /// <summary>
        /// Sends a password reset notification via WhatsApp
        /// Used for admin password reset functionality
        /// </summary>
        /// <param name="phoneNumber">User's phone number in international format</param>
        /// <param name="temporaryPassword">The temporary password generated</param>
        /// <param name="loginUrl">URL for the user to access the system</param>
        /// <returns>True if message was sent successfully</returns>
        Task<bool> SendPasswordResetNotificationAsync(string phoneNumber, string temporaryPassword, string loginUrl);

        /// <summary>
        /// Sends a general notification message via WhatsApp
        /// </summary>
        /// <param name="phoneNumber">User's phone number in international format</param>
        /// <param name="message">The message to send</param>
        /// <returns>True if message was sent successfully</returns>
        Task<bool> SendNotificationAsync(string phoneNumber, string message);

        /// <summary>
        /// Validates if a phone number is valid for WhatsApp messaging
        /// </summary>
        /// <param name="phoneNumber">Phone number to validate</param>
        /// <returns>True if the phone number is valid</returns>
        bool IsValidPhoneNumber(string phoneNumber);
    }
}

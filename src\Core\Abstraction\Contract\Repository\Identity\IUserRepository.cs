using Abstraction.Contracts.Repository;
using Domain.Entities.Users;

namespace Abstraction.Contract.Repository.Identity
{
    /// <summary>
    /// Repository interface for User entity operations
    /// </summary>
    public interface IUserRepository : IGenericRepository
    {
        /// <summary>
        /// Get user by ID
        /// </summary>
        Task<User?> GetByIdAsync(int userId);

        /// <summary>
        /// Get user roles by user ID
        /// </summary>
        Task<List<string>> GetUserRolesAsync(int userId);

        /// <summary>
        /// Get user by email
        /// </summary>
        Task<User?> GetByEmailAsync(string email);

        /// <summary>
        /// Get user by username
        /// </summary>
        Task<User?> GetByUsernameAsync(string username);

        /// <summary>
        /// Get users by role
        /// </summary>
        Task<List<User>> GetUsersByRoleAsync(string roleName);

        /// <summary>
        /// Check if user exists
        /// </summary>
        Task<bool> UserExistsAsync(int userId);

        /// <summary>
        /// Check if user has role
        /// </summary>
        Task<bool> UserHasRoleAsync(int userId, string roleName);

        /// <summary>
        /// Get active users
        /// </summary>
        Task<List<User>> GetActiveUsersAsync();

        /// <summary>
        /// Get user with roles
        /// </summary>
        Task<User?> GetUserWithRolesAsync(int userId);

        /// <summary>
        /// Search users by name or email
        /// </summary>
        Task<List<User>> SearchUsersAsync(string searchTerm);
    }
}

using Abstraction.Contract.Service.Notifications;
using Abstraction.Contracts.Logger;
using Microsoft.Extensions.Localization;
using Resources;
using System.Text.RegularExpressions;

namespace Infrastructure.Service.Notifications
{
    /// <summary>
    /// WhatsApp service implementation for sending messages
    /// This is a placeholder implementation that logs messages instead of sending them
    /// TODO: Integrate with actual WhatsApp Business API or third-party service
    /// </summary>
    public class WhatsAppService : IWhatsAppService
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public WhatsAppService(
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Public Methods
        public async Task<bool> SendRegistrationMessageAsync(string phoneNumber, string userName, string roleName, string loginUrl)
        {
            try
            {
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    _logger.LogError(null, $"Invalid phone number format: {phoneNumber}");
                    return false;
                }

                // Create localized registration message
                var message = string.Format(
                    _localizer["WhatsAppRegistrationMessage"],
                    userName,
                    roleName,
                    loginUrl
                );

                // TODO: Replace with actual WhatsApp API call
                _logger.LogInfo($"WhatsApp Registration Message would be sent to {phoneNumber}: {message}");
                
                // Simulate async operation
                await Task.Delay(100);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending WhatsApp registration message to {phoneNumber}");
                return false;
            }
        }

        public async Task<bool> SendPasswordResetNotificationAsync(string phoneNumber, string temporaryPassword, string loginUrl)
        {
            try
            {
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    _logger.LogError(null, $"Invalid phone number format: {phoneNumber}");
                    return false;
                }

                // Create localized password reset message
                var message = string.Format(
                    _localizer["WhatsAppPasswordResetMessage"],
                    temporaryPassword,
                    loginUrl
                );

                // TODO: Replace with actual WhatsApp API call
                _logger.LogInfo($"WhatsApp Password Reset Message would be sent to {phoneNumber}: [Password Hidden for Security]");
                
                // Simulate async operation
                await Task.Delay(100);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending WhatsApp password reset message to {phoneNumber}");
                return false;
            }
        }

        public async Task<bool> SendNotificationAsync(string phoneNumber, string message)
        {
            try
            {
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    _logger.LogError(null, $"Invalid phone number format: {phoneNumber}");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(message))
                {
                    _logger.LogError(null,"Message cannot be empty");
                    return false;
                }

                // TODO: Replace with actual WhatsApp API call
                _logger.LogInfo($"WhatsApp Message would be sent to {phoneNumber}: {message}");
                
                // Simulate async operation
                await Task.Delay(100);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending WhatsApp notification to {phoneNumber}");
                return false;
            }
        }

        public bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Saudi mobile pattern validation (same as used in BaseUserValidator)
            var saudiMobilePattern = @"^(\+966|966|0)?(5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$";
            return Regex.IsMatch(phoneNumber, saudiMobilePattern);
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Normalizes phone number to international format
        /// </summary>
        /// <param name="phoneNumber">Input phone number</param>
        /// <returns>Normalized phone number in +966 format</returns>
        private string NormalizePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return phoneNumber;

            // Remove all non-digit characters except +
            var cleaned = Regex.Replace(phoneNumber, @"[^\d+]", "");

            // Handle different formats
            if (cleaned.StartsWith("+966"))
                return cleaned;
            else if (cleaned.StartsWith("966"))
                return "+" + cleaned;
            else if (cleaned.StartsWith("05"))
                return "+966" + cleaned.Substring(1);
            else if (cleaned.StartsWith("5"))
                return "+966" + cleaned;

            return phoneNumber; // Return original if no pattern matches
        }
        #endregion
    }
}

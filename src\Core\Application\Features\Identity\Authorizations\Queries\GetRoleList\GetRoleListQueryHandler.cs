﻿using AutoMapper;
using Application.Features.Identity.Authorizations.Queries.Responses;
using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;



namespace Application.Features.Identity.Authorizations.Queries.GetRoleList
{
    public class GetRoleListQueryHandler : BaseResponse<PERSON><PERSON><PERSON>, IQueryHandler<GetRoleListQuery, BaseResponse<List<GetRoleListResponse>>>
    {
        #region Fileds
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        #endregion

        #region Constructors
        public GetRoleListQueryHandler(IIdentityServiceManager service, IMapper mapper, ILoggerManager logger)
        {
            _service = service;
            _mapper = mapper;
            _logger = logger;
        }
        #endregion

        #region Functions

        public async Task<BaseResponse<List<GetRoleListResponse>>> Handle(GetRoleListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var roles = await _service.AuthorizationService.GetRoleListAsync();
                if (roles == null)
                {
                    return NotFound<List<GetRoleListResponse>>("Not Found Roles");
                }

                var rolesMapper = _mapper.Map<List<GetRoleListResponse>>(roles);
                return Success(rolesMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                return ServerError<List<GetRoleListResponse>>(ex.Message);
            }
        }

        #endregion
    }
}

﻿namespace Domain.Helpers
{
    public record JwtAuthResponse
    {
        public string? AccessToken { get; set; }
        public RefreshToken refreshToken { get; set; } = null!;
        public int UserId { get; set; }
        /// <summary>
        /// Redirect URL for frontend navigation after login
        /// Sprint 3 enhancement for conditional redirection
        /// </summary>
        public bool? IsFirstLogin { get; set; }
    }

    public record RefreshToken
    {
        public string UserName { get; set; } = null!;
        public DateTime ExpireAt { get; set; }
        public string TokenString { get; set; } = null!;
    }
}

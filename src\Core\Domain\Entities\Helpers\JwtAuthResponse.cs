﻿namespace Domain.Helpers
{
    public record JwtAuthResponse
    {
        public string? AccessToken { get; set; }
        public RefreshToken refreshToken { get; set; } = null!;

        /// <summary>
        /// Indicates if user needs to reset password (first-time login)
        /// Sprint 3 enhancement for registration completion workflow
        /// </summary>
        public bool RequiresPasswordReset { get; set; } = false;

        /// <summary>
        /// Redirect URL for frontend navigation after login
        /// Sprint 3 enhancement for conditional redirection
        /// </summary>
        public string? RedirectUrl { get; set; }
    }

    public record RefreshToken
    {
        public string UserName { get; set; } = null!;
        public DateTime ExpireAt { get; set; }
        public string TokenString { get; set; } = null!;
    }
}

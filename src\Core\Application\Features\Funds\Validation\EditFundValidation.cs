﻿using Application.Features.Funds.Commands.Edit;
using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.Funds.Validation
{
    public class EditFundValidation : AbstractValidator<SaveFundCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        public EditFundValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            Include(new BaseValidation(_localizer));
            
            RuleFor(x => x.Id)
            .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
            .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField]);

            RuleFor(x => x.AttachmentId)
              .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
              .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField]);

            RuleFor(x => x.PropertiesNumber)
               .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField]);

            RuleFor(x => x.VotingTypeId)
               .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .Must(value => value >= 1 && value <= 2).WithMessage(_localizer[SharedResourcesKey.VotingTypeRangeValidator]);

            RuleFor(x => x.StrategyId)
               .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField]);

            RuleFor(x => x.FundManagers)
               .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .Must(x => x.Count >= 1 && x.Count <= 3).WithMessage(_localizer[SharedResourcesKey.FundManagersListValidation]);

            When(x => x.FundBoardSecretaries != null, () =>
            {
                RuleFor(x => x.FundBoardSecretaries)
                    .Must(list => list!.Count >= 1 && list.Count <= 4)
                    .WithMessage(_localizer[SharedResourcesKey.FundBoardSecretariesListValidation]);
            });

            RuleFor(x => x.LegalCouncilId)
               .NotEmpty().WithMessage(_localizer[SharedResourcesKey.RequiredField])
               .NotNull().WithMessage(_localizer[SharedResourcesKey.RequiredField]);

        }
    }
}

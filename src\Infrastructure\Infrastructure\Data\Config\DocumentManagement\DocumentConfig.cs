using Domain.Entities.DocumentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.DocumentManagement
{
    /// <summary>
    /// Entity configuration for Document
    /// </summary>
    public class DocumentConfig : IEntityTypeConfiguration<Document>
    {
        public void Configure(EntityTypeBuilder<Document> builder)
        {
            // Table configuration
            builder.ToTable("Documents");

            // Primary key
            builder.HasKey(x => x.Id);

            // Properties
            builder.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(x => x.Description)
                .HasMaxLength(1000);

            builder.Property(x => x.Tags)
                .HasMaxLength(500);

            builder.Property(x => x.AccessLevel)
                .IsRequired()
                .HasConversion<int>();

            // Relationships
            builder.HasOne(x => x.DocumentCategory)
                .WithMany(x => x.Documents)
                .HasForeignKey(x => x.DocumentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(x => x.UploadedByUser)
                .WithMany()
                .HasForeignKey(x => x.UploadedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(x => x.Attachment)
                .WithMany()
                .HasForeignKey(x => x.AttachmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(x => x.Name);
            builder.HasIndex(x => x.AttachmentId);
            builder.HasIndex(x => x.DocumentCategoryId);
            builder.HasIndex(x => x.UploadedByUserId);
            builder.HasIndex(x => x.AccessLevel);
            builder.HasIndex(x => x.IsActive);
            builder.HasIndex(x => x.CreatedAt);
            builder.HasIndex(x => x.DownloadCount);
            builder.HasIndex(x => x.LastAccessedAt);

            // Composite indexes for common queries
            builder.HasIndex(x => new { x.DocumentCategoryId, x.IsActive });
            builder.HasIndex(x => new { x.UploadedByUserId, x.IsActive });
            builder.HasIndex(x => new { x.AccessLevel, x.IsActive });
            builder.HasIndex(x => new { x.CreatedAt, x.IsActive });
        }
    }
}

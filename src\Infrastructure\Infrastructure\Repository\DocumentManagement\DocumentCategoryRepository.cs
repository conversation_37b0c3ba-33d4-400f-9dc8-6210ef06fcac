using Abstraction.Contract.Repository.DocumentManagement;
using Abstraction.Contract.Service;
using Domain.Entities.DocumentManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.DocumentManagement
{
    /// <summary>
    /// Repository implementation for DocumentCategory entity operations
    /// </summary>
    public class DocumentCategoryRepository : GenericRepository, IDocumentCategoryRepository
    {
        #region Constructor
        public DocumentCategoryRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get categories with optional filters
        /// </summary>
        public async Task<List<DocumentCategory>> GetCategoriesAsync(
            bool includeInactive = false,
            int? parentCategoryId = null,
            bool includeChildren = true)
        {
            var query = RepositoryContext.Set<DocumentCategory>().AsQueryable();

            if (includeChildren)
                query = query.Include(c => c.ChildCategories);

            if (!includeInactive)
                query = query.Where(c => c.IsActive);

            if (parentCategoryId.HasValue)
                query = query.Where(c => c.ParentCategoryId == parentCategoryId.Value);
            else
                query = query.Where(c => c.ParentCategoryId == null);

            return await query
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Get category hierarchy (tree structure)
        /// </summary>
        public async Task<List<DocumentCategory>> GetCategoryHierarchyAsync()
        {
            return await RepositoryContext.Set<DocumentCategory>()
                .Include(c => c.ChildCategories)
                .ThenInclude(cc => cc.ChildCategories)
                .Where(c => c.IsActive && c.ParentCategoryId == null)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Get root categories (categories without parent)
        /// </summary>
        public async Task<List<DocumentCategory>> GetRootCategoriesAsync(bool includeInactive = false)
        {
            var query = RepositoryContext.Set<DocumentCategory>()
                .Where(c => c.ParentCategoryId == null);

            if (!includeInactive)
                query = query.Where(c => c.IsActive);

            return await query
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Get child categories of a parent category
        /// </summary>
        public async Task<List<DocumentCategory>> GetChildCategoriesAsync(int parentCategoryId, bool includeInactive = false)
        {
            var query = RepositoryContext.Set<DocumentCategory>()
                .Where(c => c.ParentCategoryId == parentCategoryId);

            if (!includeInactive)
                query = query.Where(c => c.IsActive);

            return await query
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Get category with documents count
        /// </summary>
        public async Task<DocumentCategory?> GetCategoryWithDocumentCountAsync(int categoryId)
        {
            return await RepositoryContext.Set<DocumentCategory>()
                .Include(c => c.Documents)
                .FirstOrDefaultAsync(c => c.Id == categoryId);
        }

        /// <summary>
        /// Get categories by display order
        /// </summary>
        public async Task<List<DocumentCategory>> GetCategoriesByDisplayOrderAsync()
        {
            return await RepositoryContext.Set<DocumentCategory>()
                .Where(c => c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Check if category has documents
        /// </summary>
        public async Task<bool> HasDocumentsAsync(int categoryId)
        {
            return await RepositoryContext.Set<Document>()
                .AnyAsync(d => d.DocumentCategoryId == categoryId && d.IsActive);
        }

        /// <summary>
        /// Check if category has child categories
        /// </summary>
        public async Task<bool> HasChildCategoriesAsync(int categoryId)
        {
            return await RepositoryContext.Set<DocumentCategory>()
                .AnyAsync(c => c.ParentCategoryId == categoryId && c.IsActive);
        }

        /// <summary>
        /// Get category path (breadcrumb)
        /// </summary>
        public async Task<List<DocumentCategory>> GetCategoryPathAsync(int categoryId)
        {
            var path = new List<DocumentCategory>();
            var currentCategory = await RepositoryContext.Set<DocumentCategory>()
                .Include(c => c.ParentCategory)
                .FirstOrDefaultAsync(c => c.Id == categoryId);

            while (currentCategory != null)
            {
                path.Insert(0, currentCategory);
                currentCategory = currentCategory.ParentCategory;
            }

            return path;
        }

        /// <summary>
        /// Search categories by name
        /// </summary>
        public async Task<List<DocumentCategory>> SearchCategoriesAsync(string searchTerm, string culture = "en")
        {
            var lowerSearchTerm = searchTerm.ToLower();
            var query = RepositoryContext.Set<DocumentCategory>()
                .Where(c => c.IsActive);

            if (culture.StartsWith("ar"))
            {
                query = query.Where(c => c.NameAr.ToLower().Contains(lowerSearchTerm) ||
                                       (c.DescriptionAr != null && c.DescriptionAr.ToLower().Contains(lowerSearchTerm)));
            }
            else
            {
                query = query.Where(c => c.NameEn.ToLower().Contains(lowerSearchTerm) ||
                                       (c.DescriptionEn != null && c.DescriptionEn.ToLower().Contains(lowerSearchTerm)));
            }

            return await query
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.NameEn)
                .ToListAsync();
        }

        /// <summary>
        /// Get categories with document statistics
        /// </summary>
        public async Task<List<CategoryStatistics>> GetCategoriesWithStatisticsAsync()
        {
            var categories = await RepositoryContext.Set<DocumentCategory>()
                .Include(c => c.Documents)
                .Include(c => c.ChildCategories)
                .Where(c => c.IsActive)
                .ToListAsync();

            var statistics = new List<CategoryStatistics>();

            foreach (var category in categories)
            {
                var stat = new CategoryStatistics
                {
                    CategoryId = category.Id,
                    NameAr = category.NameAr,
                    NameEn = category.NameEn,
                    DocumentCount = category.Documents.Count(d => d.IsActive),
                    TotalFileSize = category.Documents.Where(d => d.IsActive).Sum(d => d.FileSize),
                    TotalDownloads = category.Documents.Where(d => d.IsActive).Sum(d => d.DownloadCount),
                    LastUploadDate = category.Documents.Where(d => d.IsActive).Max(d => (DateTime?)d.CreatedAt)
                };

                // Add child category statistics
                foreach (var child in category.ChildCategories.Where(c => c.IsActive))
                {
                    var childStat = new CategoryStatistics
                    {
                        CategoryId = child.Id,
                        NameAr = child.NameAr,
                        NameEn = child.NameEn,
                        DocumentCount = child.Documents.Count(d => d.IsActive),
                        TotalFileSize = child.Documents.Where(d => d.IsActive).Sum(d => d.FileSize),
                        TotalDownloads = child.Documents.Where(d => d.IsActive).Sum(d => d.DownloadCount),
                        LastUploadDate = child.Documents.Where(d => d.IsActive).Max(d => (DateTime?)d.CreatedAt)
                    };
                    stat.ChildCategories.Add(childStat);
                }

                statistics.Add(stat);
            }

            return statistics.OrderBy(s => s.NameEn).ToList();
        }

        /// <summary>
        /// Reorder categories
        /// </summary>
        public async Task<bool> ReorderCategoriesAsync(Dictionary<int, int> categoryDisplayOrders)
        {
            try
            {
                foreach (var kvp in categoryDisplayOrders)
                {
                    var category = await RepositoryContext.Set<DocumentCategory>()
                        .FirstOrDefaultAsync(c => c.Id == kvp.Key);
                    
                    if (category != null)
                    {
                        category.DisplayOrder = kvp.Value;
                    }
                }

                await RepositoryContext.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if category name exists
        /// </summary>
        public async Task<bool> CategoryNameExistsAsync(string nameAr, string nameEn, int? excludeCategoryId = null)
        {
            var query = RepositoryContext.Set<DocumentCategory>()
                .Where(c => c.NameAr == nameAr || c.NameEn == nameEn);

            if (excludeCategoryId.HasValue)
                query = query.Where(c => c.Id != excludeCategoryId.Value);

            return await query.AnyAsync();
        }

        /// <summary>
        /// Get category by name
        /// </summary>
        public async Task<DocumentCategory?> GetCategoryByNameAsync(string name, string culture = "en")
        {
            if (culture.StartsWith("ar"))
            {
                return await RepositoryContext.Set<DocumentCategory>()
                    .FirstOrDefaultAsync(c => c.NameAr == name && c.IsActive);
            }
            else
            {
                return await RepositoryContext.Set<DocumentCategory>()
                    .FirstOrDefaultAsync(c => c.NameEn == name && c.IsActive);
            }
        }

        #endregion
    }
}

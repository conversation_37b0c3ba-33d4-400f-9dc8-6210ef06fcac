using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Application.Features.Identity.Users.Dtos;

namespace Application.Features.Identity.Users.Queries.GetUserProfile
{
    /// <summary>
    /// Handler for getting user profile information
    /// Implements Clean Architecture and CQRS patterns
    /// </summary>
    public class GetUserProfileQueryHandler : BaseResponseHandler, IQueryHandler<GetUserProfileQuery, BaseResponse<UserProfileResponseDto>>
    {
        #region Fields
        private readonly UserManager<User> _userManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetUserProfileQueryHandler(
            UserManager<User> userManager,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _userManager = userManager;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<UserProfileResponseDto>> Handle(GetUserProfileQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Determine which user profile to get
                var targetUserId = request.UserId ?? _currentUserService.UserId.GetValueOrDefault();
                var currentUserId = _currentUserService.UserId.GetValueOrDefault();

                // Authorization check - users can only access their own profile unless they're admin
                if (targetUserId != currentUserId)
                {
                    var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                    if (currentUser == null)
                    {
                        return Unauthorized<UserProfileResponseDto>(_localizer[SharedResourcesKey.UserNotFound]);
                    }

                    var currentUserRoles = await _userManager.GetRolesAsync(currentUser);
                    if (!currentUserRoles.Contains("Admin") && !currentUserRoles.Contains("SuperAdmin"))
                    {
                        return Unauthorized<UserProfileResponseDto>(_localizer[SharedResourcesKey.UnauthorizedUserAccess]);
                    }
                }

                // Get user by ID
                var user = await _userManager.FindByIdAsync(targetUserId.ToString());
                if (user == null)
                {
                    return NotFound<UserProfileResponseDto>(_localizer[SharedResourcesKey.UserNotFound]);
                }

                // Get user roles
                var userRoles = await _userManager.GetRolesAsync(user);

                // Map to response DTO
                var response = _mapper.Map<UserProfileResponseDto>(user);
                response.Roles = userRoles.ToList();
               

                return Success(response);
            }
            catch (Exception ex)
            {
                return ServerError<UserProfileResponseDto>(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }
        #endregion
    }
}

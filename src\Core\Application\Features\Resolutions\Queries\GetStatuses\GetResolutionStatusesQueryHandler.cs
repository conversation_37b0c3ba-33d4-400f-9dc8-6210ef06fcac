using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.ResolutionManagement;
using System.ComponentModel;
using Abstraction.Contract.Service;
using Abstraction.Constants;

namespace Application.Features.Resolutions.Queries.GetStatuses
{
    /// <summary>
    /// Handler for getting all available resolution statuses
    /// Returns localized status information for frontend dropdowns and filters
    /// Uses LocalizedDto pattern for automatic culture-based localization
    /// Implements role-based filtering: Fund Manager (all), Legal Council/Board Secretary (all except draft), Board Members (voting statuses only)
    /// </summary>
    public class GetResolutionStatusesQueryHandler : Base<PERSON><PERSON>ponse<PERSON>and<PERSON>, IQueryHandler<GetResolutionStatusesQuery, BaseResponse<List<ResolutionStatusDto>>>
    {
        #region Fields
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetResolutionStatusesQueryHandler(ICurrentUserService currentUserService)
        {
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<List<ResolutionStatusDto>>> Handle(GetResolutionStatusesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var statuses = new List<ResolutionStatusDto>();

                // Get all enum values
                var enumValues = Enum.GetValues<ResolutionStatusEnum>();

                // Get current user roles for filtering
                var userRoles = _currentUserService.Roles;
                var allowedStatuses = GetAllowedStatusesForUser(userRoles);

                foreach (var status in enumValues)
                {
                    // Filter statuses based on user role
                    if (!allowedStatuses.Contains(status))
                        continue;

                    var statusDto = new ResolutionStatusDto
                    {
                        Value = status,
                        Id = (int)status,
                        NameEn = GetEnglishStatusName(status),
                        NameAr = GetArabicStatusName(status),
                        Description = GetStatusDescription(status)
                    };

                    statuses.Add(statusDto);
                }

                // Sort by ID for consistent ordering
                statuses = statuses.OrderBy(s => s.Id).ToList();

                return Success(statuses);
            }
            catch (Exception ex)
            {
                return ServerError<List<ResolutionStatusDto>>($"Error retrieving resolution statuses: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets allowed statuses based on user role
        /// Fund Manager: All statuses
        /// Legal Council/Board Secretary: All statuses except Draft
        /// Board Members: VotingInProgress, Approved, NotApproved
        /// </summary>
        private HashSet<ResolutionStatusEnum> GetAllowedStatusesForUser(IList<string> userRoles)
        {
            // Check if user is Fund Manager - can see all statuses
            if (RoleHelper.IsFundManager(userRoles))
            {
                return new HashSet<ResolutionStatusEnum>(Enum.GetValues<ResolutionStatusEnum>());
            }

            // Check if user is Legal Council or Board Secretary - can see all except Draft
            if (RoleHelper.IsLegalCouncil(userRoles) || RoleHelper.IsBoardSecretary(userRoles))
            {
                var allStatuses = new HashSet<ResolutionStatusEnum>(Enum.GetValues<ResolutionStatusEnum>());
                allStatuses.Remove(ResolutionStatusEnum.Draft);
                return allStatuses;
            }

            // Check if user is Board Member - can see only voting statuses
            if (RoleHelper.IsBoardMember(userRoles))
            {
                return new HashSet<ResolutionStatusEnum>
                {
                    ResolutionStatusEnum.VotingInProgress,
                    ResolutionStatusEnum.Approved,
                    ResolutionStatusEnum.NotApproved
                };
            }

            // Default: no statuses allowed for unauthorized users
            return new HashSet<ResolutionStatusEnum>();
        }

        /// <summary>
        /// Gets the English status name
        /// </summary>
        private string GetEnglishStatusName(ResolutionStatusEnum status)
        {
            return status switch
            {
                ResolutionStatusEnum.Draft => "Draft",
                ResolutionStatusEnum.Pending => "Pending",
                ResolutionStatusEnum.CompletingData => "Completing Data",
                ResolutionStatusEnum.WaitingForConfirmation => "Waiting for Confirmation",
                ResolutionStatusEnum.Confirmed => "Confirmed",
                ResolutionStatusEnum.Rejected => "Rejected",
                ResolutionStatusEnum.VotingInProgress => "Voting in Progress",
                ResolutionStatusEnum.Approved => "Approved",
                ResolutionStatusEnum.NotApproved => "Not Approved",
                ResolutionStatusEnum.Cancelled => "Cancelled",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Gets the Arabic status name
        /// </summary>
        private string GetArabicStatusName(ResolutionStatusEnum status)
        {
            return status switch
            {
                ResolutionStatusEnum.Draft => "مسودة",
                ResolutionStatusEnum.Pending => "معلق",
                ResolutionStatusEnum.CompletingData => "استكمال البيانات",
                ResolutionStatusEnum.WaitingForConfirmation => "في انتظار التأكيد",
                ResolutionStatusEnum.Confirmed => "مؤكد",
                ResolutionStatusEnum.Rejected => "مرفوض",
                ResolutionStatusEnum.VotingInProgress => "التصويت جاري",
                ResolutionStatusEnum.Approved => "معتمد",
                ResolutionStatusEnum.NotApproved => "غير معتمد",
                ResolutionStatusEnum.Cancelled => "ملغي",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Gets the status description from the Description attribute
        /// </summary>
        private string GetStatusDescription(ResolutionStatusEnum status)
        {
            var field = status.GetType().GetField(status.ToString());
            var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                 .FirstOrDefault() as DescriptionAttribute;
            return attribute?.Description ?? status.ToString();
        }

        #endregion
    }
}

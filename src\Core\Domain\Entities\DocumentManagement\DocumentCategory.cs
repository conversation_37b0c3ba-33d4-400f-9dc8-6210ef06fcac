using Domain.Entities.Base;

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Represents a document category for organizing documents
    /// </summary>
    public class DocumentCategory : FullAuditedEntity
    {
        /// <summary>
        /// Category name in Arabic
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Category name in English
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Navigation property to documents in this category
        /// </summary>
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }
}

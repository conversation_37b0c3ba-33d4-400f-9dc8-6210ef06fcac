using Domain.Entities.Base;

namespace Domain.Entities.DocumentManagement
{
    /// <summary>
    /// Represents a document category for organizing documents
    /// </summary>
    public class DocumentCategory : FullAuditedEntity
    {
        /// <summary>
        /// Category name in Arabic
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Category name in English
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        /// <summary>
        /// Category description in Arabic
        /// </summary>
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Category description in English
        /// </summary>
        public string? DescriptionEn { get; set; }

        /// <summary>
        /// Category icon/symbol
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Whether category is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Parent category ID for hierarchical structure
        /// </summary>
        public int? ParentCategoryId { get; set; }

        /// <summary>
        /// Navigation property to parent category
        /// </summary>
        public virtual DocumentCategory? ParentCategory { get; set; }

        /// <summary>
        /// Navigation property to child categories
        /// </summary>
        public virtual ICollection<DocumentCategory> ChildCategories { get; set; } = new List<DocumentCategory>();

        /// <summary>
        /// Navigation property to documents in this category
        /// </summary>
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();

        /// <summary>
        /// Maximum file size allowed in this category (in bytes)
        /// </summary>
        public long? MaxFileSize { get; set; }

        /// <summary>
        /// Allowed file extensions (comma-separated)
        /// </summary>
        public string? AllowedExtensions { get; set; }

        /// <summary>
        /// Category color for UI display
        /// </summary>
        public string? Color { get; set; }
    }
}

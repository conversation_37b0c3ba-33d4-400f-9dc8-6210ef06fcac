using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.DocumentManagement.Dtos;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Resources;
using System.Globalization;

namespace Application.Features.DocumentManagement.Queries.GetDocumentCategories
{
    /// <summary>
    /// Handler for getting document categories
    /// </summary>
    public class GetDocumentCategoriesQueryHandler : BaseResponseHandler, IQueryHandler<GetDocumentCategoriesQuery, BaseResponse<List<DocumentCategoryDto>>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public GetDocumentCategoriesQueryHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<List<DocumentCategoryDto>>> Handle(GetDocumentCategoriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting document categories for user {_currentUserService.UserId}");

                // Get categories
                var categories = await _repositoryManager.DocumentCategoryRepository.GetCategoriesAsync(
                    includeInactive: request.IncludeInactive,
                    parentCategoryId: request.ParentCategoryId);

                // Map to DTOs
                var categoryDtos = new List<DocumentCategoryDto>();
                var culture = CultureInfo.CurrentCulture.Name;
                var isArabic = culture.StartsWith("ar");

                foreach (var category in categories)
                {
                    var dto = _mapper.Map<DocumentCategoryDto>(category);
                    
                    // Set localized names
                    dto.Name = isArabic ? category.NameAr : category.NameEn;
                    dto.Description = isArabic ? category.DescriptionAr : category.DescriptionEn;
                    
                    // Set parent category name if exists
                    if (category.ParentCategory != null)
                    {
                        dto.ParentCategoryName = isArabic ? category.ParentCategory.NameAr : category.ParentCategory.NameEn;
                    }

                    // Get document count if requested
                    if (request.IncludeDocumentCounts)
                    {
                        dto.DocumentCount = await _repositoryManager.DocumentRepository.GetDocumentCountByCategoryAsync(category.Id);
                    }

                    // Format max file size
                    if (category.MaxFileSize.HasValue)
                    {
                        dto.FormattedMaxFileSize = FormatFileSize(category.MaxFileSize.Value);
                    }

                    // Check if user can upload to this category
                    dto.CanUpload = await CanUserUploadToCategory(category.Id);

                    // Get child categories if requested
                    if (request.IncludeChildCategories && category.ChildCategories.Any())
                    {
                        dto.ChildCategories = await GetChildCategoriesAsync(category.ChildCategories, isArabic);
                    }

                    categoryDtos.Add(dto);
                }

                // Sort by display order then by name
                categoryDtos = categoryDtos
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToList();

                return Success(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting document categories: {ex.Message}");
                return ServerError<List<DocumentCategoryDto>>(_localizer["ErrorGettingDocumentCategories"]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Get child categories recursively
        /// </summary>
        private async Task<List<DocumentCategoryDto>> GetChildCategoriesAsync(
            ICollection<Domain.Entities.DocumentManagement.DocumentCategory> childCategories, 
            bool isArabic)
        {
            var childDtos = new List<DocumentCategoryDto>();

            foreach (var child in childCategories.Where(c => c.IsActive))
            {
                var childDto = _mapper.Map<DocumentCategoryDto>(child);
                childDto.Name = isArabic ? child.NameAr : child.NameEn;
                childDto.Description = isArabic ? child.DescriptionAr : child.DescriptionEn;
                
                // Get document count
                childDto.DocumentCount = await _repositoryManager.DocumentRepository.GetDocumentCountByCategoryAsync(child.Id);
                
                // Check upload permission
                childDto.CanUpload = await CanUserUploadToCategory(child.Id);

                // Format max file size
                if (child.MaxFileSize.HasValue)
                {
                    childDto.FormattedMaxFileSize = FormatFileSize(child.MaxFileSize.Value);
                }

                // Recursively get child categories
                if (child.ChildCategories.Any())
                {
                    childDto.ChildCategories = await GetChildCategoriesAsync(child.ChildCategories, isArabic);
                }

                childDtos.Add(childDto);
            }

            return childDtos.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name).ToList();
        }

        /// <summary>
        /// Check if current user can upload to category
        /// </summary>
        private async Task<bool> CanUserUploadToCategory(int categoryId)
        {
            // Get user roles
            var userRoles = await _repositoryManager.UserRepository.GetUserRolesAsync(_currentUserService.UserId);
            
            // Admins can upload to any category
            if (userRoles.Any(r => r.Equals("Admin", StringComparison.OrdinalIgnoreCase) || 
                                 r.Equals("Super Admin", StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // For now, allow all authenticated users to upload
            // This can be extended with more granular permissions
            return true;
        }

        /// <summary>
        /// Format file size in human readable format
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        #endregion
    }
}

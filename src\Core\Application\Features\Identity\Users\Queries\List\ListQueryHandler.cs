﻿using AutoMapper;
using Application.Features.Identity.Users.Queries.Responses;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Common.Wappers;
using Abstraction.Contracts.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;



namespace Application.Features.Identity.Users.Queries.List
{

    /// <summary>
    /// Enhanced handler for user listing with advanced filtering capabilities
    /// Sprint 3 enhancement with role, status, and registration filtering
    /// </summary>
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, PaginatedResult<GetUserListResponse>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructors
        public ListQueryHandler(
            IIdentityServiceManager service,
            IMapper mapper,
            ILoggerManager logger,
            IIdentityServiceManager identityServiceManager,
            IStringLocalizer<SharedResources> localizer)
        {
            _mapper = mapper;
            _service = service;
            _logger = logger;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
        }
        #endregion

        #region Functions
        public async Task<PaginatedResult<GetUserListResponse>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Use the optimized UsersWithRoles method to eagerly load roles
                var users = _service.UserManagmentService.UsersWithRoles().AsQueryable();

                // Sprint 3 Enhancement: Apply advanced filtering
                users = await ApplyFiltersAsync(users, request);
                if (!users.Any())
                {
                    return PaginatedResult<GetUserListResponse>.EmptyCollection(_localizer[SharedResourcesKey.NoRecords]);
                }

                // Project to DTO and paginate - roles are now automatically mapped via navigation properties
                var paginatedList = await _mapper.ProjectTo<GetUserListResponse>(users).ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);

                // No need for EnhanceWithRoleInformation - roles are loaded via navigation properties
                _logger.LogInfo($"Successfully retrieved {paginatedList.Data.Count} users with roles using optimized query");

                return paginatedList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Enhanced User List Query");
                return PaginatedResult<GetUserListResponse>.ServerError(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }

        /// <summary>
        /// Apply advanced filtering based on Sprint 3 requirements
        /// </summary>
        private async Task<IQueryable<User>> ApplyFiltersAsync(IQueryable<User> users, ListQuery request)
        {
            // Basic search filter
            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                users = users.Where(u =>u.UserName != null && u.UserName.Contains(request.Search));
            }

            // Advanced search filter
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                users = users.Where(u => u.FullName.Contains(request.Name));
            }

            // Status filter (active/inactive)
            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                {
                    // Active users: not locked out or lockout has expired
                    users = users.Where(u => u.IsActive);
                }
                else
                {
                    // Inactive users: locked out and lockout is still active
                    users = users.Where(u => !u.IsActive);
                }
            }
            // Role filter (requires separate handling due to many-to-many relationship)
            if (!string.IsNullOrWhiteSpace(request.Role))
            {
                var usersInRole = await _identityServiceManager.UserManagmentService.GetUsersByRole(request.Role);
                var userIdsInRole = usersInRole.Select(u => u.Id).ToList();
                users = users.Where(u => userIdsInRole.Contains(u.Id));
            }

            return users;
        }

    
        /// <summary>
        /// DEPRECATED: Enhance response DTOs with role information
        /// This method has been replaced by the optimized navigation property approach
        /// Kept for reference and backward compatibility if needed
        ///
        /// The new approach uses Entity Framework navigation properties to eagerly load roles,
        /// eliminating the N+1 query problem that this method had
        /// </summary>
        /// <param name="userResponses">List of user response DTOs to enhance with role information</param>
        [Obsolete("This method has been replaced by navigation property mapping for better performance")]
        private async Task EnhanceWithRoleInformation(List<GetUserListResponse> userResponses)
        {
            foreach (var userResponse in userResponses)
            {
                try
                {
                    // Find user by ID
                    var user = await _identityServiceManager.UserManagmentService.FindByIdAsync(userResponse.Id.ToString());
                    if (user != null)
                    {
                        // Get user roles from authorization service
                        var roles = await _identityServiceManager.AuthorizationService.GetUsersRoles(user);
                        if (roles?.UserRoles != null)
                        {
                            // Filter to only roles where HasRole = true (roles the user actually has)
                            var userActualRoles = roles.UserRoles
                                .Where(r => r.HasRole)
                                .Select(r => r.Name)
                                .ToList();

                            userResponse.Roles = userActualRoles;
                            userResponse.PrimaryRole = userActualRoles.FirstOrDefault();

                            _logger.LogInfo($"Successfully retrieved {userActualRoles.Count} roles for user ID: {userResponse.Id}");
                        }
                        else
                        {
                            // Handle case where roles response is null
                            _logger.LogWarn($"Roles response is null for user ID: {userResponse.Id}");
                            userResponse.Roles = new List<string>();
                            userResponse.PrimaryRole = null;
                        }
                    }
                    else
                    {
                        // Handle case where user is not found
                        _logger.LogWarn($"User not found with ID: {userResponse.Id}");
                        userResponse.Roles = new List<string>();
                        userResponse.PrimaryRole = null;
                    }
                }
                catch (Exception ex)
                {
                    // Log error and continue processing other users
                    _logger.LogError(ex, $"Error retrieving roles for user ID: {userResponse.Id}. Error: {ex.Message}");

                    // Set default empty values to ensure response consistency
                    userResponse.Roles = new List<string>();
                    userResponse.PrimaryRole = null;
                }
            }
        }
        #endregion
    }
}

﻿using AutoMapper;
using Application.Features.Identity.Users.Queries.Responses;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Common.Wappers;
using Abstraction.Contracts.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;



namespace Application.Features.Identity.Users.Queries.List
{

    /// <summary>
    /// Enhanced handler for user listing with advanced filtering capabilities
    /// Sprint 3 enhancement with role, status, and registration filtering
    /// </summary>
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, PaginatedResult<GetUserListResponse>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructors
        public ListQueryHandler(
            IIdentityServiceManager service,
            IMapper mapper,
            ILoggerManager logger,
            IIdentityServiceManager identityServiceManager,
            IStringLocalizer<SharedResources> localizer)
        {
            _mapper = mapper;
            _service = service;
            _logger = logger;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
        }
        #endregion

        #region Functions
        public async Task<PaginatedResult<GetUserListResponse>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Use the optimized UsersWithRoles method to eagerly load roles
                var users = _service.UserManagmentService.UsersWithRoles().AsQueryable();

                // Sprint 3 Enhancement: Apply advanced filtering
                users = await ApplyFiltersAsync(users, request);
                if (!users.Any())
                {
                    return PaginatedResult<GetUserListResponse>.EmptyCollection(_localizer[SharedResourcesKey.NoRecords]);
                }

                // Project to DTO and paginate - roles are now automatically mapped via navigation properties
                var paginatedList = await _mapper.ProjectTo<GetUserListResponse>(users).ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);

                // No need for EnhanceWithRoleInformation - roles are loaded via navigation properties
                _logger.LogInfo($"Successfully retrieved {paginatedList.Data.Count} users with roles using optimized query");

                return paginatedList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Enhanced User List Query");
                return PaginatedResult<GetUserListResponse>.ServerError(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }

        /// <summary>
        /// Apply advanced filtering based on Sprint 3 requirements
        /// </summary>
        private async Task<IQueryable<User>> ApplyFiltersAsync(IQueryable<User> users, ListQuery request)
        {
            // Basic search filter
            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                users = users.Where(u =>u.UserName != null && u.UserName.Contains(request.Search));
            }

            // Advanced search filter
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                users = users.Where(u => u.FullName.Contains(request.Name));
            }

            // Status filter (active/inactive)
            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                {
                    // Active users: not locked out or lockout has expired
                    users = users.Where(u => u.IsActive);
                }
                else
                {
                    // Inactive users: locked out and lockout is still active
                    users = users.Where(u => !u.IsActive);
                }
            }
            // Role filter (requires separate handling due to many-to-many relationship)
            if (!string.IsNullOrWhiteSpace(request.Role))
            {
                var usersInRole = await _identityServiceManager.UserManagmentService.GetUsersByRole(request.Role);
                var userIdsInRole = usersInRole.Select(u => u.Id).ToList();
                users = users.Where(u => userIdsInRole.Contains(u.Id));
            }

            return users;
        }

    
       
        #endregion
    }
}

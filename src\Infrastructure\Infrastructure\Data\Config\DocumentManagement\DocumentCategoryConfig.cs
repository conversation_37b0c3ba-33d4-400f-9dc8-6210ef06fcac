using Domain.Entities.DocumentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.DocumentManagement
{
    /// <summary>
    /// Entity configuration for DocumentCategory
    /// </summary>
    public class DocumentCategoryConfig : IEntityTypeConfiguration<DocumentCategory>
    {
        public void Configure(EntityTypeBuilder<DocumentCategory> builder)
        {
            // Table configuration
            builder.ToTable("DocumentCategories");

            // Primary key
            builder.HasKey(x => x.Id);

            // Properties
            builder.Property(x => x.NameAr)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(x => x.NameEn)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(x => x.DescriptionAr)
                .HasMaxLength(1000);

            builder.Property(x => x.DescriptionEn)
                .HasMaxLength(1000);

            builder.Property(x => x.Icon)
                .HasMaxLength(100);

            builder.Property(x => x.Color)
                .HasMaxLength(7); // For hex color codes

            builder.Property(x => x.AllowedExtensions)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(x => x.ParentCategory)
                .WithMany(x => x.ChildCategories)
                .HasForeignKey(x => x.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(x => x.Documents)
                .WithOne(x => x.DocumentCategory)
                .HasForeignKey(x => x.DocumentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(x => x.NameAr);
            builder.HasIndex(x => x.NameEn);
            builder.HasIndex(x => x.ParentCategoryId);
            builder.HasIndex(x => x.DisplayOrder);
            builder.HasIndex(x => x.IsActive);

            // Seed data
            SeedData(builder);
        }

        private void SeedData(EntityTypeBuilder<DocumentCategory> builder)
        {
            var categories = new List<DocumentCategory>
            {
                // Root categories
                new DocumentCategory
                {
                    Id = 1,
                    NameAr = "الوثائق القانونية",
                    NameEn = "Legal Documents",
                    DescriptionAr = "الوثائق والمستندات القانونية",
                    DescriptionEn = "Legal documents and contracts",
                    Icon = "fas fa-gavel",
                    Color = "#3498db",
                    DisplayOrder = 1,
                    IsActive = true,
                    AllowedExtensions = ".pdf,.doc,.docx",
                    MaxFileSize = 50 * 1024 * 1024, // 50 MB
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 2,
                    NameAr = "التقارير المالية",
                    NameEn = "Financial Reports",
                    DescriptionAr = "التقارير والبيانات المالية",
                    DescriptionEn = "Financial reports and statements",
                    Icon = "fas fa-chart-line",
                    Color = "#2ecc71",
                    DisplayOrder = 2,
                    IsActive = true,
                    AllowedExtensions = ".pdf,.xls,.xlsx,.csv",
                    MaxFileSize = 25 * 1024 * 1024, // 25 MB
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 3,
                    NameAr = "محاضر الاجتماعات",
                    NameEn = "Meeting Minutes",
                    DescriptionAr = "محاضر اجتماعات مجلس الإدارة واللجان",
                    DescriptionEn = "Board and committee meeting minutes",
                    Icon = "fas fa-users",
                    Color = "#e74c3c",
                    DisplayOrder = 3,
                    IsActive = true,
                    AllowedExtensions = ".pdf,.doc,.docx",
                    MaxFileSize = 20 * 1024 * 1024, // 20 MB
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 4,
                    NameAr = "السياسات والإجراءات",
                    NameEn = "Policies & Procedures",
                    DescriptionAr = "السياسات والإجراءات التنظيمية",
                    DescriptionEn = "Organizational policies and procedures",
                    Icon = "fas fa-book",
                    Color = "#9b59b6",
                    DisplayOrder = 4,
                    IsActive = true,
                    AllowedExtensions = ".pdf,.doc,.docx",
                    MaxFileSize = 30 * 1024 * 1024, // 30 MB
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 5,
                    NameAr = "الوثائق العامة",
                    NameEn = "General Documents",
                    DescriptionAr = "الوثائق والمستندات العامة",
                    DescriptionEn = "General documents and files",
                    Icon = "fas fa-folder",
                    Color = "#95a5a6",
                    DisplayOrder = 5,
                    IsActive = true,
                    AllowedExtensions = ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.zip",
                    MaxFileSize = 100 * 1024 * 1024, // 100 MB
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },

                // Sub-categories for Legal Documents
                new DocumentCategory
                {
                    Id = 6,
                    NameAr = "العقود",
                    NameEn = "Contracts",
                    DescriptionAr = "العقود والاتفاقيات",
                    DescriptionEn = "Contracts and agreements",
                    Icon = "fas fa-handshake",
                    Color = "#3498db",
                    DisplayOrder = 1,
                    IsActive = true,
                    ParentCategoryId = 1,
                    AllowedExtensions = ".pdf,.doc,.docx",
                    MaxFileSize = 50 * 1024 * 1024,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 7,
                    NameAr = "التراخيص",
                    NameEn = "Licenses",
                    DescriptionAr = "التراخيص والتصاريح",
                    DescriptionEn = "Licenses and permits",
                    Icon = "fas fa-certificate",
                    Color = "#3498db",
                    DisplayOrder = 2,
                    IsActive = true,
                    ParentCategoryId = 1,
                    AllowedExtensions = ".pdf,.jpg,.jpeg,.png",
                    MaxFileSize = 25 * 1024 * 1024,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },

                // Sub-categories for Financial Reports
                new DocumentCategory
                {
                    Id = 8,
                    NameAr = "التقارير السنوية",
                    NameEn = "Annual Reports",
                    DescriptionAr = "التقارير المالية السنوية",
                    DescriptionEn = "Annual financial reports",
                    Icon = "fas fa-calendar-alt",
                    Color = "#2ecc71",
                    DisplayOrder = 1,
                    IsActive = true,
                    ParentCategoryId = 2,
                    AllowedExtensions = ".pdf,.xls,.xlsx",
                    MaxFileSize = 25 * 1024 * 1024,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                },
                new DocumentCategory
                {
                    Id = 9,
                    NameAr = "التقارير الربعية",
                    NameEn = "Quarterly Reports",
                    DescriptionAr = "التقارير المالية الربعية",
                    DescriptionEn = "Quarterly financial reports",
                    Icon = "fas fa-chart-bar",
                    Color = "#2ecc71",
                    DisplayOrder = 2,
                    IsActive = true,
                    ParentCategoryId = 2,
                    AllowedExtensions = ".pdf,.xls,.xlsx",
                    MaxFileSize = 20 * 1024 * 1024,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = 1
                }
            };

            builder.HasData(categories);
        }
    }
}

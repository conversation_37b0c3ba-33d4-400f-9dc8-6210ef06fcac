using Application.Features.DocumentManagement.Commands.UploadDocument;
using FluentValidation;

namespace Application.Features.DocumentManagement.Validation
{
    /// <summary>
    /// Validator for UploadDocumentCommand
    /// </summary>
    public class UploadDocumentCommandValidator : AbstractValidator<UploadDocumentCommand>
    {
        public UploadDocumentCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Document name is required")
                .MaximumLength(255).WithMessage("Document name cannot exceed 255 characters")
                .Matches(@"^[^<>:""/\\|?*]+$").WithMessage("Document name contains invalid characters");

            RuleFor(x => x.DocumentCategoryId)
                .GreaterThan(0).WithMessage("Valid document category is required");

            RuleFor(x => x.Description)
                .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");

            RuleFor(x => x.Tags)
                .MaximumLength(500).WithMessage("Tags cannot exceed 500 characters");

            RuleFor(x => x.AccessLevel)
                .InclusiveBetween(1, 3).WithMessage("Access level must be between 1 and 3");

            RuleFor(x => x.File)
                .NotNull().WithMessage("File is required")
                .Must(BeAValidFile).WithMessage("Invalid file")
                .Must(HaveValidSize).WithMessage("File size must be between 1 byte and 100 MB")
                .Must(HaveValidExtension).WithMessage("File type is not allowed");
        }

        /// <summary>
        /// Validate that file is not null and has content
        /// </summary>
        private bool BeAValidFile(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            return file != null && file.Length > 0;
        }

        /// <summary>
        /// Validate file size (max 100 MB)
        /// </summary>
        private bool HaveValidSize(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            if (file == null) return false;
            
            const long maxFileSize = 100 * 1024 * 1024; // 100 MB
            return file.Length > 0 && file.Length <= maxFileSize;
        }

        /// <summary>
        /// Validate file extension
        /// </summary>
        private bool HaveValidExtension(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            if (file == null) return false;

            var allowedExtensions = new[]
            {
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                ".txt", ".rtf", ".csv",
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg",
                ".zip", ".rar", ".7z",
                ".mp4", ".avi", ".mov", ".wmv", ".flv",
                ".mp3", ".wav", ".wma", ".aac"
            };

            var fileExtension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            return !string.IsNullOrEmpty(fileExtension) && allowedExtensions.Contains(fileExtension);
        }
    }
}

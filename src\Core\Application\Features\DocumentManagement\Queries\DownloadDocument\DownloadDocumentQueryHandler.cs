using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Domain.Entities.DocumentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.DocumentManagement.Queries.DownloadDocument
{
    /// <summary>
    /// Handler for downloading documents
    /// </summary>
    public class DownloadDocumentQueryHandler : BaseResponseHandler, IQueryHandler<DownloadDocumentQuery, BaseResponse<DocumentDownloadResult>>
    {
        #region Fields
        private readonly IRepositoryManager _repositoryManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public DownloadDocumentQueryHandler(
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer)
        {
            _repositoryManager = repositoryManager;
            _currentUserService = currentUserService;
            _logger = logger;
            _localizer = localizer;
        }
        #endregion

        #region Handle
        public async Task<BaseResponse<DocumentDownloadResult>> Handle(DownloadDocumentQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting document download for document ID: {request.DocumentId} by user {_currentUserService.UserId}");

                // Get document with attachment
                var document = await _repositoryManager.DocumentRepository.GetByIdAsync<Document>(request.DocumentId, false,
                    query => query.Include(d => d.Attachment));
                if (document == null)
                {
                    return NotFound<DocumentDownloadResult>(_localizer["DocumentNotFound"]);
                }

                // Check permissions
                var canDownload = await CanUserDownloadDocument(document);
                if (!canDownload)
                {
                    return Unauthorized<DocumentDownloadResult>(_localizer["UnauthorizedToDownloadDocument"]);
                }

                // Get file path from attachment
                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", document.Attachment.Path.TrimStart('/'));

                if (!File.Exists(fullPath))
                {
                    _logger.LogWarn($"Physical file not found: {fullPath}");
                    return NotFound<DocumentDownloadResult>(_localizer["FileNotFoundOnDisk"]);
                }

                // Read file content
                var fileContent = await File.ReadAllBytesAsync(fullPath, cancellationToken);

                // Update download count and last accessed date (only for actual downloads, not previews)
                if (!request.IsPreview)
                {
                    document.DownloadCount++;
                    document.LastAccessedAt = DateTime.UtcNow;
                    await _repositoryManager.DocumentRepository.UpdateAsync(document);
                    await _repositoryManager.SaveAsync();
                    
                    _logger.LogInfo($"Document downloaded successfully. ID: {document.Id}, Download count: {document.DownloadCount}");
                }
                else
                {
                    _logger.LogInfo($"Document previewed. ID: {document.Id}");
                }

                // Prepare result
                var result = new DocumentDownloadResult
                {
                    FileContent = fileContent,
                    FileName = document.Attachment.FileName,
                    ContentType = document.Attachment.ContentType,
                    FileSize = document.Attachment.FileSize,
                    FileExists = true
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading document ID {request.DocumentId}: {ex.Message}");
                
                var errorResult = new DocumentDownloadResult
                {
                    FileExists = false,
                    ErrorMessage = _localizer["DocumentDownloadFailed"]
                };
                
                return ServerError<DocumentDownloadResult>(_localizer["DocumentDownloadFailed"]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Check if current user can download the document
        /// </summary>
        private async Task<bool> CanUserDownloadDocument(Domain.Entities.DocumentManagement.Document document)
        {
            // Get user roles
            var userRoles = await _repositoryManager.UserRepository.GetUserRolesAsync(_currentUserService.UserId ?? 1);
            bool isAdmin = userRoles.Any(r => r.Equals("Admin", StringComparison.OrdinalIgnoreCase) || 
                                            r.Equals("Super Admin", StringComparison.OrdinalIgnoreCase));

            return document.AccessLevel switch
            {
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Public => true,
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Private => document.UploadedByUserId == (_currentUserService.UserId ?? 1) || isAdmin,
                Domain.Entities.DocumentManagement.DocumentAccessLevel.Restricted => document.UploadedByUserId == (_currentUserService.UserId ?? 1) || isAdmin,
                _ => false
            };
        }
        #endregion
    }
}
